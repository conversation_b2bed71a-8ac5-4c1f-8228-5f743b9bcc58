"""
Administration endpoints for system management
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Dict, Any
from datetime import datetime, timedelta

from src.database.connection import get_db
from src.database.models import User, Household, Prediction, AuditLog, SystemMetrics
from src.api.routers.auth import get_current_active_user, require_role, log_user_action
from src.api.schemas.admin import (
    SystemStats, UserManagement, AuditLogResponse, 
    SystemMetricsResponse, UserCreate, UserUpdate
)
from src.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.get("/stats", response_model=SystemStats)
async def get_system_statistics(
    current_user: User = Depends(require_role("admin")),
    db: Session = Depends(get_db)
):
    """Get system-wide statistics"""
    try:
        # User statistics
        total_users = db.query(User).count()
        active_users = db.query(User).filter(User.is_active == True).count()
        
        # Household statistics
        total_households = db.query(Household).count()
        households_last_30_days = db.query(Household).filter(
            Household.created_at >= datetime.utcnow() - timedelta(days=30)
        ).count()
        
        # Prediction statistics
        total_predictions = db.query(Prediction).count()
        predictions_last_30_days = db.query(Prediction).filter(
            Prediction.created_at >= datetime.utcnow() - timedelta(days=30)
        ).count()
        
        # Poverty statistics
        income_poor_count = db.query(Prediction).filter(
            Prediction.income_poor == True
        ).count()
        
        multidimensional_poor_count = db.query(Prediction).filter(
            Prediction.multidimensionally_poor == True
        ).count()
        
        return SystemStats(
            total_users=total_users,
            active_users=active_users,
            total_households=total_households,
            households_last_30_days=households_last_30_days,
            total_predictions=total_predictions,
            predictions_last_30_days=predictions_last_30_days,
            income_poor_percentage=(income_poor_count / total_predictions * 100) if total_predictions > 0 else 0,
            multidimensional_poor_percentage=(multidimensional_poor_count / total_predictions * 100) if total_predictions > 0 else 0
        )
        
    except Exception as e:
        logger.error(f"Failed to get system statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve system statistics"
        )


@router.get("/users", response_model=List[UserManagement])
async def list_all_users(
    limit: int = 100,
    offset: int = 0,
    current_user: User = Depends(require_role("admin")),
    db: Session = Depends(get_db)
):
    """List all users for management"""
    try:
        users = db.query(User).order_by(
            User.created_at.desc()
        ).offset(offset).limit(limit).all()
        
        return [UserManagement.from_orm(user) for user in users]
        
    except Exception as e:
        logger.error(f"Failed to list users: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve users"
        )


@router.post("/users", response_model=UserManagement)
async def create_user_admin(
    user_data: UserCreate,
    request: Request,
    current_user: User = Depends(require_role("admin")),
    db: Session = Depends(get_db)
):
    """Create a new user (admin only)"""
    try:
        # Check if user already exists
        if db.query(User).filter(User.username == user_data.username).first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already exists"
            )
        
        if db.query(User).filter(User.email == user_data.email).first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already exists"
            )
        
        # Create user
        from passlib.context import CryptContext
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        user = User(
            username=user_data.username,
            email=user_data.email,
            hashed_password=pwd_context.hash(user_data.password),
            full_name=user_data.full_name,
            role=user_data.role,
            is_active=True,
            is_verified=True
        )
        
        db.add(user)
        db.commit()
        db.refresh(user)
        
        # Log action
        await log_user_action(
            db, current_user, "create_user", request,
            resource_type="user", resource_id=str(user.id),
            request_data={"username": user_data.username, "role": user_data.role}
        )
        
        logger.info(f"User created by admin: {user_data.username}")
        
        return UserManagement.from_orm(user)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user"
        )


@router.put("/users/{user_id}", response_model=UserManagement)
async def update_user_admin(
    user_id: int,
    user_update: UserUpdate,
    request: Request,
    current_user: User = Depends(require_role("admin")),
    db: Session = Depends(get_db)
):
    """Update user information (admin only)"""
    try:
        user = db.query(User).filter(User.id == user_id).first()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Update fields
        update_data = user_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(user, field, value)
        
        user.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(user)
        
        # Log action
        await log_user_action(
            db, current_user, "update_user", request,
            resource_type="user", resource_id=str(user.id),
            request_data=update_data
        )
        
        logger.info(f"User updated by admin: {user.username}")
        
        return UserManagement.from_orm(user)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user"
        )


@router.delete("/users/{user_id}")
async def deactivate_user(
    user_id: int,
    request: Request,
    current_user: User = Depends(require_role("admin")),
    db: Session = Depends(get_db)
):
    """Deactivate user account"""
    try:
        user = db.query(User).filter(User.id == user_id).first()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        if user.id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot deactivate your own account"
            )
        
        user.is_active = False
        user.updated_at = datetime.utcnow()
        db.commit()
        
        # Log action
        await log_user_action(
            db, current_user, "deactivate_user", request,
            resource_type="user", resource_id=str(user.id)
        )
        
        logger.info(f"User deactivated by admin: {user.username}")
        
        return {"message": "User deactivated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to deactivate user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to deactivate user"
        )


@router.get("/audit-logs", response_model=List[AuditLogResponse])
async def get_audit_logs(
    limit: int = 100,
    offset: int = 0,
    action: str = None,
    user_id: int = None,
    current_user: User = Depends(require_role("admin")),
    db: Session = Depends(get_db)
):
    """Get audit logs for system monitoring"""
    try:
        query = db.query(AuditLog)
        
        # Apply filters
        if action:
            query = query.filter(AuditLog.action == action)
        
        if user_id:
            query = query.filter(AuditLog.user_id == user_id)
        
        # Get logs with pagination
        logs = query.order_by(
            AuditLog.timestamp.desc()
        ).offset(offset).limit(limit).all()
        
        return [AuditLogResponse.from_orm(log) for log in logs]
        
    except Exception as e:
        logger.error(f"Failed to get audit logs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve audit logs"
        )


@router.get("/metrics", response_model=List[SystemMetricsResponse])
async def get_system_metrics(
    metric_name: str = None,
    hours: int = 24,
    current_user: User = Depends(require_role("admin")),
    db: Session = Depends(get_db)
):
    """Get system performance metrics"""
    try:
        query = db.query(SystemMetrics).filter(
            SystemMetrics.timestamp >= datetime.utcnow() - timedelta(hours=hours)
        )
        
        if metric_name:
            query = query.filter(SystemMetrics.metric_name == metric_name)
        
        metrics = query.order_by(SystemMetrics.timestamp.desc()).all()
        
        return [SystemMetricsResponse.from_orm(metric) for metric in metrics]
        
    except Exception as e:
        logger.error(f"Failed to get system metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve system metrics"
        )
