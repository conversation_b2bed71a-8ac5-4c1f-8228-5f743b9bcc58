# Poverty Classification System - User Guide

## Overview

The Poverty Classification System is a comprehensive platform for analyzing and classifying household poverty using advanced machine learning techniques. This guide will help you navigate and use the system effectively.

## Getting Started

### 1. Accessing the System

- **Web Interface:** http://localhost:8501
- **API Documentation:** http://localhost:8000/docs

### 2. Login

Use your provided credentials to access the system:

- **Username:** Your assigned username
- **Password:** Your assigned password

**Default Demo Accounts:**
- Admin: `admin` / `admin123!`
- Analyst: `analyst1` / `analyst123!`
- Viewer: `viewer1` / `viewer123!`

⚠️ **Change default passwords in production environments!**

## User Roles

### Viewer
- View predictions and analytics
- Make individual household predictions
- Access basic reporting features

### Analyst
- All Viewer permissions
- Perform batch analysis
- Access advanced analytics
- Export data and reports

### Admin
- All Analyst permissions
- User management
- System administration
- Access audit logs and system metrics

## Features

### 1. Dashboard
The dashboard provides an overview of:
- System status and health
- Recent predictions
- Quick statistics (for analysts/admins)
- Quick action buttons

### 2. Individual Prediction

#### Input Categories
Fill out household information across five categories:

**Demographics:**
- Household size
- District location
- Number of employed members
- Transportation access

**Education:**
- Highest education level
- Average years of education
- School attendance ratio
- Education-related debt

**Health:**
- Chronic illness indicators
- Health insurance status
- Disability status
- Healthcare access and debt

**Living Standards:**
- Housing quality
- Sanitation facilities
- Water access
- Durable goods ownership

**Economic:**
- Various debt categories
- Market access
- Service accessibility

#### Results
The system provides:
- **Income Prediction:** Estimated monthly household income
- **Poverty Classification:** Poor/Non-poor status
- **Dimension Analysis:** Education, health, living standards, and multidimensional poverty
- **Confidence Score:** Prediction reliability
- **Recommendations:** Targeted intervention suggestions

### 3. Batch Analysis

Upload CSV or Excel files containing multiple household records for bulk analysis:

1. **File Upload:** Support for CSV, XLSX, XLS formats
2. **Processing:** Automated analysis of all households
3. **Results:** Comprehensive summary and detailed results
4. **Export:** Download results in CSV format

#### File Format Requirements
Your file should include columns such as:
- `household_size`: Number of household members
- `district`: Geographic location
- `education_level_max`: Highest education level
- `chronic_illness`: Health indicator (true/false)
- `housing_quality_index`: Housing quality (0-10 scale)
- Additional columns as needed

### 4. Analytics & Reports

View comprehensive analytics including:
- **Time Series:** Prediction trends over time
- **Distribution Charts:** Poverty rate distributions
- **Income Analysis:** Income distribution patterns
- **Export Options:** Download charts and data

## Best Practices

### Data Input
1. **Completeness:** Fill out as many fields as possible for accurate predictions
2. **Accuracy:** Ensure data reflects actual household conditions
3. **Consistency:** Use consistent units and scales

### Batch Processing
1. **File Size:** Limit batch uploads to 1000 households maximum
2. **Data Quality:** Clean and validate data before upload
3. **Column Mapping:** Ensure column names match expected format

### Security
1. **Password Management:** Change default passwords immediately
2. **Session Management:** Log out when finished
3. **Data Privacy:** Handle household data according to privacy regulations

## Troubleshooting

### Common Issues

**Login Problems:**
- Verify username and password
- Check if account is active
- Contact administrator if issues persist

**Prediction Errors:**
- Ensure all required fields are filled
- Check data format and ranges
- Verify API connectivity

**File Upload Issues:**
- Check file format (CSV, XLSX, XLS only)
- Verify file size (max 50MB)
- Ensure proper column headers

**Performance Issues:**
- Large batch files may take time to process
- Check internet connectivity
- Contact support for persistent issues

### Error Messages

**"Session expired":** Login again to refresh your session
**"Validation error":** Check input data format and ranges
**"Access denied":** Insufficient permissions for requested action
**"API Error":** System connectivity issue, try again or contact support

## Support

For technical support:
- **Email:** <EMAIL>
- **Documentation:** Check this guide and API documentation
- **System Status:** Monitor the dashboard health indicators

## Data Privacy

The system handles sensitive household data. Please ensure:
- Compliance with local data protection regulations
- Proper consent for data collection and analysis
- Secure handling of personal information
- Regular data backup and security reviews

## Updates and Maintenance

- System updates are applied automatically
- Scheduled maintenance windows are announced in advance
- Feature updates and improvements are released regularly
- Check the dashboard for system status and announcements
