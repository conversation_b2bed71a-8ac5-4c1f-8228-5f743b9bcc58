#!/usr/bin/env python3
"""
Database initialization script for Poverty Classification System
Creates tables and initial data
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from passlib.context import CryptContext
import logging

from config.settings import settings
from src.database.models import Base, User
from src.core.logging import setup_logging

# Setup logging
logger = setup_logging()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def create_database_tables():
    """Create all database tables"""
    try:
        engine = create_engine(settings.DATABASE_URL)
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
        return engine
    except Exception as e:
        logger.error(f"Failed to create database tables: {e}")
        raise


def create_default_admin_user(engine):
    """Create default admin user"""
    try:
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        # Check if admin user already exists
        admin_user = db.query(User).filter(User.username == "admin").first()
        if admin_user:
            logger.info("Admin user already exists")
            return
        
        # Create admin user
        hashed_password = pwd_context.hash("admin123!")  # Change this in production
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=hashed_password,
            full_name="System Administrator",
            role="admin",
            is_active=True,
            is_verified=True
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        logger.info("Default admin user created successfully")
        logger.warning("Default admin password is 'admin123!' - CHANGE THIS IN PRODUCTION!")
        
    except Exception as e:
        logger.error(f"Failed to create admin user: {e}")
        raise
    finally:
        db.close()


def create_sample_users(engine):
    """Create sample users for testing"""
    try:
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        sample_users = [
            {
                "username": "analyst1",
                "email": "<EMAIL>",
                "password": "analyst123!",
                "full_name": "Data Analyst",
                "role": "analyst"
            },
            {
                "username": "viewer1",
                "email": "<EMAIL>",
                "password": "viewer123!",
                "full_name": "Report Viewer",
                "role": "viewer"
            }
        ]
        
        for user_data in sample_users:
            # Check if user already exists
            existing_user = db.query(User).filter(User.username == user_data["username"]).first()
            if existing_user:
                continue
            
            # Create user
            hashed_password = pwd_context.hash(user_data["password"])
            user = User(
                username=user_data["username"],
                email=user_data["email"],
                hashed_password=hashed_password,
                full_name=user_data["full_name"],
                role=user_data["role"],
                is_active=True,
                is_verified=True
            )
            
            db.add(user)
        
        db.commit()
        logger.info("Sample users created successfully")
        
    except Exception as e:
        logger.error(f"Failed to create sample users: {e}")
        raise
    finally:
        db.close()


def create_directories():
    """Create necessary directories"""
    directories = [
        "logs",
        "uploads",
        "models",
        "exports"
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(exist_ok=True)
        logger.info(f"Created directory: {directory}")


def main():
    """Main initialization function"""
    logger.info("Starting database initialization...")
    
    try:
        # Create directories
        create_directories()
        
        # Create database tables
        engine = create_database_tables()
        
        # Create default users
        create_default_admin_user(engine)
        
        # Create sample users (only in development)
        if settings.ENVIRONMENT == "development":
            create_sample_users(engine)
        
        logger.info("Database initialization completed successfully!")
        
        # Print connection info
        logger.info(f"Database URL: {settings.DATABASE_URL}")
        logger.info(f"Environment: {settings.ENVIRONMENT}")
        
        if settings.ENVIRONMENT == "development":
            logger.info("\nDefault users created:")
            logger.info("Admin: username=admin, password=admin123!")
            logger.info("Analyst: username=analyst1, password=analyst123!")
            logger.info("Viewer: username=viewer1, password=viewer123!")
            logger.warning("\nCHANGE DEFAULT PASSWORDS IN PRODUCTION!")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
