# Poverty Classification System - API Documentation

## Overview

The Poverty Classification System provides a comprehensive RESTful API for integrating poverty analysis capabilities into external systems. The API supports authentication, household management, predictions, and administrative functions.

## Base URL

```
Production: https://your-domain.com/api/v1
Development: http://localhost:8000/api/v1
```

## Authentication

The API uses JWT (JSON Web Token) based authentication.

### Login
```http
POST /auth/login
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 1800
}
```

### Using Tokens
Include the access token in the Authorization header:
```http
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

### Refresh Token
```http
POST /auth/refresh
Authorization: Bearer <refresh_token>
```

## Endpoints

### Health Check
```http
GET /health
```
Check system health and status.

### Authentication Endpoints

#### Get Current User
```http
GET /auth/me
Authorization: Bearer <token>
```

#### Change Password
```http
POST /auth/change-password
Authorization: Bearer <token>
Content-Type: application/json

{
  "current_password": "old_password",
  "new_password": "new_password"
}
```

#### Logout
```http
POST /auth/logout
Authorization: Bearer <token>
```

### Prediction Endpoints

#### Single Household Prediction
```http
POST /predictions/predict
Authorization: Bearer <token>
Content-Type: application/json

{
  "household_size": 4,
  "district": "Colombo",
  "education_level_max": 12,
  "education_years_mean": 8.5,
  "school_attendance_ratio": 0.9,
  "chronic_illness": false,
  "health_insurance": true,
  "disability": false,
  "hospital_time": 30.0,
  "housing_quality_index": 6.5,
  "adequate_sanitation": true,
  "good_drinking_water": true,
  "weighted_durable_index": 5.2,
  "num_employed": 2,
  "bus_halt_time": 15.0,
  "market_time": 20.0,
  "school_time": 25.0,
  "education_debt": 0.0,
  "medical_debt": 5000.0,
  "housing_debt": 150000.0,
  "business_debt": 0.0,
  "other_debt": 10000.0
}
```

**Response:**
```json
{
  "prediction_id": "uuid-string",
  "household_id": "household-uuid",
  "predicted_income": 75000.0,
  "income_poor": false,
  "education_poor": false,
  "health_poor": false,
  "living_standards_poor": false,
  "multidimensionally_poor": false,
  "probabilities": {
    "education_poor": 0.15,
    "health_poor": 0.20,
    "living_standards_poor": 0.10,
    "multidimensional_poor": 0.12
  },
  "confidence": 0.85,
  "recommendations": {
    "health": ["Consider health insurance upgrade"],
    "economic": ["Debt management counseling"]
  },
  "model_version": "1.0.0",
  "poverty_threshold": 50000.0,
  "created_at": "2024-01-15T10:30:00Z"
}
```

#### Batch Prediction
```http
POST /predictions/batch-predict
Authorization: Bearer <token>
Content-Type: multipart/form-data

file: <csv_or_excel_file>
```

**Response:**
```json
{
  "batch_id": "batch-uuid",
  "total_predictions": 100,
  "successful_predictions": 98,
  "failed_predictions": 2,
  "predictions": [
    {
      "prediction_id": "uuid-1",
      "household_id": "household-1",
      "predicted_income": 45000.0,
      "income_poor": true,
      "multidimensionally_poor": true,
      "created_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### Prediction History
```http
GET /predictions/history?limit=50&offset=0
Authorization: Bearer <token>
```

#### Get Prediction Details
```http
GET /predictions/{prediction_id}
Authorization: Bearer <token>
```

### Household Management

#### Create Household
```http
POST /households/
Authorization: Bearer <token>
Content-Type: application/json

{
  "household_id": "HH001",
  "household_size": 4,
  "district": "Colombo",
  "education_level_max": 12,
  // ... other household data
}
```

#### List Households
```http
GET /households/?limit=50&offset=0&district=Colombo
Authorization: Bearer <token>
```

#### Get Household
```http
GET /households/{household_id}
Authorization: Bearer <token>
```

#### Update Household
```http
PUT /households/{household_id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "household_size": 5,
  "education_level_max": 14
}
```

#### Delete Household
```http
DELETE /households/{household_id}
Authorization: Bearer <token>
```

### Admin Endpoints (Admin Only)

#### System Statistics
```http
GET /admin/stats
Authorization: Bearer <admin_token>
```

**Response:**
```json
{
  "total_users": 25,
  "active_users": 20,
  "total_households": 1500,
  "households_last_30_days": 150,
  "total_predictions": 2500,
  "predictions_last_30_days": 300,
  "income_poor_percentage": 35.2,
  "multidimensional_poor_percentage": 28.7
}
```

#### User Management
```http
GET /admin/users
POST /admin/users
PUT /admin/users/{user_id}
DELETE /admin/users/{user_id}
Authorization: Bearer <admin_token>
```

#### Audit Logs
```http
GET /admin/audit-logs?action=login&user_id=123
Authorization: Bearer <admin_token>
```

#### System Metrics
```http
GET /admin/metrics?metric_name=cpu_usage&hours=24
Authorization: Bearer <admin_token>
```

## Error Handling

The API uses standard HTTP status codes:

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

**Error Response Format:**
```json
{
  "error": true,
  "message": "Validation error",
  "details": [
    {
      "field": "household_size",
      "message": "Value must be between 1 and 20"
    }
  ],
  "status_code": 422,
  "timestamp": 1642248600.123
}
```

## Rate Limiting

- **General endpoints:** 100 requests per minute
- **Prediction endpoints:** 50 requests per minute
- **Batch endpoints:** 10 requests per minute
- **Admin endpoints:** 200 requests per minute

## Data Validation

### Household Data Constraints
- `household_size`: 1-20
- `education_level_max`: 0-20
- `education_years_mean`: 0-25
- `school_attendance_ratio`: 0.0-1.0
- `housing_quality_index`: 0.0-10.0
- `weighted_durable_index`: 0.0-10.0
- All debt fields: >= 0.0
- All time fields: >= 0.0

## SDK Examples

### Python
```python
import requests

class PovertyClassifierAPI:
    def __init__(self, base_url, username, password):
        self.base_url = base_url
        self.token = self._login(username, password)
    
    def _login(self, username, password):
        response = requests.post(
            f"{self.base_url}/auth/login",
            json={"username": username, "password": password}
        )
        return response.json()["access_token"]
    
    def predict(self, household_data):
        headers = {"Authorization": f"Bearer {self.token}"}
        response = requests.post(
            f"{self.base_url}/predictions/predict",
            json=household_data,
            headers=headers
        )
        return response.json()

# Usage
api = PovertyClassifierAPI("http://localhost:8000/api/v1", "username", "password")
result = api.predict({
    "household_size": 4,
    "district": "Colombo",
    # ... other data
})
```

### JavaScript
```javascript
class PovertyClassifierAPI {
    constructor(baseUrl) {
        this.baseUrl = baseUrl;
        this.token = null;
    }
    
    async login(username, password) {
        const response = await fetch(`${this.baseUrl}/auth/login`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({username, password})
        });
        const data = await response.json();
        this.token = data.access_token;
        return data;
    }
    
    async predict(householdData) {
        const response = await fetch(`${this.baseUrl}/predictions/predict`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
            },
            body: JSON.stringify(householdData)
        });
        return await response.json();
    }
}
```

## Support

For API support and questions:
- **Email:** <EMAIL>
- **Documentation:** http://localhost:8000/docs (Interactive API docs)
- **Status Page:** http://localhost:8000/health
