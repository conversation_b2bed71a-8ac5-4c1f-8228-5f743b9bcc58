from streamlit.testing.v1 import AppTest

def test_form_validation():
    at = AppTest.from_file("app.py")
    # Increase timeout to 20 seconds to avoid script run timeout
    at.run(timeout=20)
    # Check for error message when models are not trained
    assert at.error[0].value == "Please train the models first by uploading a dataset or using the default models."
    # Simulate clicking the 'Use Default Models' button if present
    if at.button("Use Default Models").exists():
        at.button("Use Default Models").click()
        at.run(timeout=20)
        # After models are trained, check that the error is gone
        assert not at.error.exists() or all(
            "Please train the models first" not in e.value for e in at.error
        )
    # You can expand this test to fill in form fields and check for submission success

pytest test_app_ui.py
