import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

def generate_sample_data(n_samples=1000, random_seed=42):
    """
    Generates synthetic sample household data with realistic characteristics.
    This is used when no custom dataset is uploaded.
    
    Parameters:
    -----------
    n_samples : int
        Number of samples to generate
    random_seed : int
        Random seed for reproducibility
    
    Returns:
    --------
    pandas.DataFrame
        DataFrame containing generated sample data
    """
    np.random.seed(random_seed)
    
    # Generate basic features
    data = {
        # Household characteristics
        'hhsize': np.random.randint(1, 8, n_samples),
        'education_level_max': np.random.randint(0, 6, n_samples),
        'education_years_mean': np.random.uniform(0, 15, n_samples),
        'school_attendance_ratio': np.random.uniform(0, 1, n_samples),
        
        # Health indicators
        'chronic_illness': np.random.randint(0, 2, n_samples),
        'health_insurance': np.random.randint(0, 2, n_samples),
        'disability': np.random.randint(0, 2, n_samples),
        
        # Housing and living standards
        'housing_quality_index': np.random.uniform(0, 10, n_samples),
        'adequate_sanitation': np.random.randint(0, 2, n_samples),
        'good_drinking_water': np.random.randint(0, 2, n_samples),
        'weighted_durable_index': np.random.uniform(0, 20, n_samples),
        
        # Debt columns
        'housing_debt': np.random.uniform(0, 500000, n_samples),
        'education_debt': np.random.uniform(0, 200000, n_samples),
        'medical_debt': np.random.uniform(0, 150000, n_samples),
        'business_debt': np.random.uniform(0, 300000, n_samples),
        'other_debt': np.random.uniform(0, 100000, n_samples),
        
        # Accessibility times (in minutes)
        'bus_halt_time': np.random.uniform(0, 60, n_samples),
        'primary_school_time': np.random.uniform(0, 45, n_samples),
        'secondary_school_time': np.random.uniform(0, 60, n_samples),
        'hospital_time': np.random.uniform(0, 90, n_samples),
        'market_time': np.random.uniform(0, 60, n_samples),
    }
    
    # Convert to DataFrame
    df = pd.DataFrame(data)
    
    # Generate income with some correlation to features
    # Base income
    base_income = 30000 + np.random.normal(0, 5000, n_samples)
    
    # Income modifiers based on features
    modifiers = (
        df['education_level_max'] * 8000 +
        df['education_years_mean'] * 2000 -
        df['hhsize'] * 3000 +
        df['health_insurance'] * 10000 -
        df['chronic_illness'] * 8000 -
        df['disability'] * 12000 +
        df['housing_quality_index'] * 2000 +
        df['adequate_sanitation'] * 5000 +
        df['good_drinking_water'] * 5000 +
        df['weighted_durable_index'] * 1000 -
        np.log1p(df['hospital_time']) * 2000
    )
    
    # Calculate final income with some noise
    df['hhincomepm'] = np.maximum(base_income + modifiers + np.random.normal(0, 10000, n_samples), 5000)
    
    # Calculate total debt and debt-to-income ratio
    df['total_debt'] = df['housing_debt'] + df['education_debt'] + df['medical_debt'] + df['business_debt'] + df['other_debt']
    df['debt_to_income'] = df['total_debt'] / df['hhincomepm']
    
    # Calculate accessibility score (lower is better)
    df['access_score'] = (df['bus_halt_time'] + 
                         df['primary_school_time'] + 
                         df['secondary_school_time'] + 
                         df['hospital_time'] + 
                         df['market_time']) / 5
    
    # Generate poverty dimension indicators
    # Income poverty (below threshold of 50,000 LKR)
    df['income_poor'] = (df['hhincomepm'] < 50000).astype(int)
    
    # Education poverty
    education_factors = (
        -df['education_level_max'] * 0.6 -
        df['education_years_mean'] * 0.5 -
        df['school_attendance_ratio'] * 2.0 +
        df['education_debt'] * 0.00001
    )
    df['education_poor'] = (education_factors + np.random.normal(0, 1, n_samples) > 0).astype(int)
    
    # Health poverty
    health_factors = (
        -df['health_insurance'] * 2 +
        df['chronic_illness'] * 2 +
        df['disability'] * 2 +
        df['hospital_time'] * 0.02 +
        df['medical_debt'] * 0.00001 -
        df['hhincomepm'] * 0.00002
    )
    df['health_poor'] = (health_factors + np.random.normal(0, 1, n_samples) > 0).astype(int)
    
    # Living standards poverty
    living_standards_factors = (
        -df['housing_quality_index'] * 0.5 -
        df['adequate_sanitation'] * 1.5 -
        df['good_drinking_water'] * 1.5 -
        df['weighted_durable_index'] * 0.2 +
        df['access_score'] * 0.05 +
        df['housing_debt'] * 0.000005
    )
    df['living_standards_poor'] = (living_standards_factors + np.random.normal(0, 1, n_samples) > 0).astype(int)
    
    # Multidimensional poverty (poor in at least 2 dimensions)
    poverty_count = df['income_poor'] + df['education_poor'] + df['health_poor'] + df['living_standards_poor']
    df['multidimensionally_poor'] = (poverty_count >= 2).astype(int)
    
    return df

def prepare_features(df):
    """
    Prepares features for model training/prediction.
    Includes feature engineering specific to household survey data.
    
    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame containing raw data
    
    Returns:
    --------
    pandas.DataFrame
        DataFrame containing prepared features
    """
    # Make a copy to avoid modifying the original
    df_copy = df.copy()
    
    # Engineer total debt if individual debt columns exist but total_debt doesn't
    debt_columns = ['housing_debt', 'education_debt', 'medical_debt', 'business_debt', 'other_debt']
    if 'total_debt' not in df_copy.columns and all(col in df_copy.columns for col in debt_columns):
        df_copy['total_debt'] = df_copy[debt_columns].sum(axis=1)
    
    # Engineer debt-to-income ratio if not present
    if 'debt_to_income' not in df_copy.columns and 'total_debt' in df_copy.columns and 'hhincomepm' in df_copy.columns:
        df_copy['debt_to_income'] = df_copy['total_debt'] / df_copy['hhincomepm'].replace(0, 1)  # Avoid division by zero
    
    # Engineer accessibility score if not present but component times are available
    time_columns = ['bus_halt_time', 'primary_school_time', 'secondary_school_time', 'hospital_time', 'market_time']
    if 'access_score' not in df_copy.columns and all(col in df_copy.columns for col in time_columns):
        df_copy['access_score'] = df_copy[time_columns].mean(axis=1)
    
    # For any missing engineered features, add placeholder columns
    # This ensures the model can still make predictions without these specific features
    # These would be replaced by actual values in a real implementation
    
    # Drop target columns if present
    target_columns = ['hhincomepm', 'income_poor', 'education_poor', 'health_poor', 'living_standards_poor', 'multidimensionally_poor']
    feature_df = df_copy.drop(columns=[col for col in target_columns if col in df_copy.columns], errors='ignore')
    
    return feature_df

def load_and_prepare_data(uploaded_file, target_columns):
    """
    Loads data from an uploaded file and prepares it for model training.
    
    Parameters:
    -----------
    uploaded_file : streamlit.UploadedFile
        The uploaded dataset file
    target_columns : list
        List of column names to be used as targets
        
    Returns:
    --------
    tuple
        (X, y_dict) where X is the feature DataFrame and 
        y_dict is a dictionary of target columns
    """
    try:
        # Check file extension to determine the file type
        file_extension = uploaded_file.name.split('.')[-1].lower()
        
        if file_extension == 'csv':
            df = pd.read_csv(uploaded_file)
        elif file_extension in ['xlsx', 'xls']:
            df = pd.read_excel(uploaded_file)
        else:
            raise ValueError(f"Unsupported file format: {file_extension}. Please upload a CSV or Excel file.")
        
        # Rename column if it exists with a different name but same meaning
        column_mapping = {
            'household_income': 'hhincomepm',
            'income': 'hhincomepm',
            'household_size': 'hhsize',
            'has_chronic_illness': 'chronic_illness',
            'has_health_insurance': 'health_insurance',
            'has_disability': 'disability',
            'water_quality': 'good_drinking_water',
            'sanitation_quality': 'adequate_sanitation'
        }
        
        for old_col, new_col in column_mapping.items():
            if old_col in df.columns and new_col not in df.columns:
                df.rename(columns={old_col: new_col}, inplace=True)
        
        # Apply feature engineering
        df = engineer_hies_features(df)
        
        # Extract target variables that exist in the dataset
        y_dict = {}
        for col in target_columns:
            if col in df.columns:
                if col == 'hhincomepm':  # Income column
                    y_dict[col] = df[col]
                elif col.endswith('_poor'):  # Poverty dimension columns
                    # Ensure binary
                    y_dict[col] = df[col].astype(int)
        
        # Apply feature selection/preparation
        X = prepare_features(df)
        
        return X, y_dict
        
    except Exception as e:
        raise Exception(f"Error processing the uploaded file: {str(e)}")

def engineer_hies_features(df):
    """
    Applies feature engineering specific to household survey data.
    This includes calculating derived features and poverty dimensions.
    
    Parameters:
    -----------
    df : pandas.DataFrame
        Raw data from HIES dataset
    
    Returns:
    --------
    pandas.DataFrame
        DataFrame with engineered features
    """
    # Create a copy to avoid modifying the original
    df_copy = df.copy()
    
    # Engineer debt columns with more realistic values if they don't exist
    debt_columns = ['housing_debt', 'education_debt', 'medical_debt', 'business_debt', 'other_debt']
    missing_debt_columns = [col for col in debt_columns if col not in df_copy.columns]
    
    # Generate realistic debt values based on income if available
    if missing_debt_columns:
        if 'hhincomepm' in df_copy.columns:
            # Use income-based patterns for different debt types
            mean_income = df_copy['hhincomepm'].mean()
            for col in missing_debt_columns:
                if col == 'housing_debt':
                    # Housing debt typically higher - random percentage of households
                    mask = np.random.random(len(df_copy)) < 0.3  # 30% have housing debt
                    df_copy[col] = np.where(mask, df_copy['hhincomepm'] * np.random.uniform(10, 30, len(df_copy)), 0)
                elif col == 'education_debt':
                    mask = np.random.random(len(df_copy)) < 0.2  # 20% have education debt
                    df_copy[col] = np.where(mask, df_copy['hhincomepm'] * np.random.uniform(2, 8, len(df_copy)), 0)
                elif col == 'medical_debt':
                    mask = np.random.random(len(df_copy)) < 0.25  # 25% have medical debt
                    df_copy[col] = np.where(mask, df_copy['hhincomepm'] * np.random.uniform(1, 6, len(df_copy)), 0)
                elif col == 'business_debt':
                    mask = np.random.random(len(df_copy)) < 0.15  # 15% have business debt
                    df_copy[col] = np.where(mask, df_copy['hhincomepm'] * np.random.uniform(5, 20, len(df_copy)), 0)
                else:  # other_debt
                    mask = np.random.random(len(df_copy)) < 0.4  # 40% have other debt
                    df_copy[col] = np.where(mask, df_copy['hhincomepm'] * np.random.uniform(1, 4, len(df_copy)), 0)
        else:
            # Without income information, use reasonable defaults based on Sri Lanka context
            for col in missing_debt_columns:
                mask = np.random.random(len(df_copy)) < 0.25  # 25% have some form of debt
                # Use median values that make sense for Sri Lanka
                if col == 'housing_debt':
                    df_copy[col] = np.where(mask, np.random.uniform(100000, 500000, len(df_copy)), 0)
                elif col == 'education_debt':
                    df_copy[col] = np.where(mask, np.random.uniform(30000, 150000, len(df_copy)), 0)
                elif col == 'medical_debt':
                    df_copy[col] = np.where(mask, np.random.uniform(20000, 100000, len(df_copy)), 0)
                elif col == 'business_debt':
                    df_copy[col] = np.where(mask, np.random.uniform(50000, 300000, len(df_copy)), 0)
                else:  # other_debt
                    df_copy[col] = np.where(mask, np.random.uniform(10000, 80000, len(df_copy)), 0)
    
    # Calculate total debt
    df_copy['total_debt'] = df_copy[debt_columns].sum(axis=1)
    
    # Calculate debt-to-income ratio if income column exists
    if 'hhincomepm' in df_copy.columns:
        # Ensure income is positive to avoid negative debt-to-income ratio
        df_copy['hhincomepm'] = df_copy['hhincomepm'].clip(lower=10000)  # Minimum income threshold
        df_copy['debt_to_income'] = df_copy['total_debt'] / df_copy['hhincomepm']  # Now safe from division by zero
    
    # Calculate accessibility score if time columns exist
    time_columns = ['bus_halt_time', 'primary_school_time', 'secondary_school_time', 'hospital_time', 'market_time']
    available_time_columns = [col for col in time_columns if col in df_copy.columns]
    
    if available_time_columns:
        df_copy['access_score'] = df_copy[available_time_columns].mean(axis=1)
    
    # If income data exists, calculate income_poor using the default threshold
    if 'hhincomepm' in df_copy.columns and 'income_poor' not in df_copy.columns:
        df_copy['income_poor'] = (df_copy['hhincomepm'] < 50000).astype(int)
    
    # Engineering poverty dimension indicators if they don't exist already
    # Education poverty
    if 'education_poor' not in df_copy.columns:
        ed_factors = []
        
        if 'education_level_max' in df_copy.columns:
            ed_factors.append(df_copy['education_level_max'] < 2)
            
        if 'education_years_mean' in df_copy.columns:
            ed_factors.append(df_copy['education_years_mean'] < 8)
            
        if 'school_attendance_ratio' in df_copy.columns:
            ed_factors.append(df_copy['school_attendance_ratio'] < 0.8)
        
        if ed_factors:
            # Consider education poor if at least one factor is true
            df_copy['education_poor'] = (sum(ed_factors) > 0).astype(int)
    
    # Health poverty
    if 'health_poor' not in df_copy.columns:
        health_factors = []
        
        if 'health_insurance' in df_copy.columns:
            health_factors.append(df_copy['health_insurance'] == 0)
            
        if 'chronic_illness' in df_copy.columns:
            health_factors.append(df_copy['chronic_illness'] == 1)
            
        if 'disability' in df_copy.columns:
            health_factors.append(df_copy['disability'] == 1)
            
        if 'hospital_time' in df_copy.columns:
            health_factors.append(df_copy['hospital_time'] > 30)
        
        if health_factors:
            # Consider health poor if at least one factor is true
            df_copy['health_poor'] = (sum(health_factors) > 0).astype(int)
    
    # Living standards poverty
    if 'living_standards_poor' not in df_copy.columns:
        ls_factors = []
        
        if 'housing_quality_index' in df_copy.columns:
            ls_factors.append(df_copy['housing_quality_index'] < 5)
            
        if 'adequate_sanitation' in df_copy.columns:
            ls_factors.append(df_copy['adequate_sanitation'] == 0)
            
        if 'good_drinking_water' in df_copy.columns:
            ls_factors.append(df_copy['good_drinking_water'] == 0)
            
        if 'weighted_durable_index' in df_copy.columns:
            ls_factors.append(df_copy['weighted_durable_index'] < 10)
            
        if 'access_score' in df_copy.columns:
            ls_factors.append(df_copy['access_score'] > 30)
        
        if ls_factors:
            # Consider living standards poor if at least two factors are true
            df_copy['living_standards_poor'] = (sum(ls_factors) >= 2).astype(int)
    
    # Multidimensional poverty
    poverty_columns = ['income_poor', 'education_poor', 'health_poor', 'living_standards_poor']
    available_poverty_columns = [col for col in poverty_columns if col in df_copy.columns]
    
    if available_poverty_columns and 'multidimensionally_poor' not in df_copy.columns:
        # Sum available poverty indicators
        poverty_count = df_copy[available_poverty_columns].sum(axis=1)
        # Multidimensionally poor if poor in at least 2 dimensions
        df_copy['multidimensionally_poor'] = (poverty_count >= 2).astype(int)
    
    return df_copy

def plot_income_prediction(predicted_income, threshold):
    """
    Creates a visualization of the income prediction compared to the poverty threshold.
    
    Parameters:
    -----------
    predicted_income : float
        Predicted household income
    threshold : float
        Poverty income threshold
    
    Returns:
    --------
    matplotlib.figure.Figure
        Figure object containing the visualization
    """
    fig, ax = plt.subplots(figsize=(8, 4))
    
    # Create a horizontal bar chart
    values = [predicted_income, threshold]
    labels = ['Predicted Income', 'Poverty Threshold']
    colors = ['#00897B' if predicted_income >= threshold else '#E53935', '#FF9800']
    
    ax.barh(labels, values, color=colors)
    
    # Add values on the bars
    for i, v in enumerate(values):
        ax.text(v + 1000, i, f'LKR {v:,.0f}', va='center')
    
    # Add a vertical line at the threshold
    ax.axvline(threshold, color='#FF9800', linestyle='--', alpha=0.7)
    
    # Set title and labels
    ax.set_title('Income Prediction vs Poverty Threshold')
    ax.set_xlabel('Sri Lankan Rupees (LKR)')
    
    # Remove top and right spines
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    
    # Add grid
    ax.grid(axis='x', linestyle='--', alpha=0.6)
    
    plt.tight_layout()
    return fig

def plot_poverty_dimensions(dimension_results):
    """
    Creates a visualization of poverty dimensions classification.
    
    Parameters:
    -----------
    dimension_results : dict
        Dictionary containing results for each poverty dimension
    
    Returns:
    --------
    matplotlib.figure.Figure
        Figure object containing the visualization
    """
    fig, ax = plt.subplots(figsize=(8, 4))
    
    # Prepare data
    dimensions = ['Income', 'Education', 'Health', 'Living Standards', 'Multidimensional']
    values = [
        dimension_results.get('income_poor', 0),
        dimension_results.get('education_poor', 0),
        dimension_results.get('health_poor', 0),
        dimension_results.get('living_standards_poor', 0),
        dimension_results.get('multidimensionally_poor', 0)
    ]
    
    # Create color map based on classification
    colors = ['#E53935' if v else '#00897B' for v in values]
    
    # Create horizontal bar chart
    ax.barh(dimensions, [1] * len(dimensions), color=colors)
    
    # Add classification labels
    for i, v in enumerate(values):
        ax.text(0.5, i, 'POOR' if v else 'NOT POOR', 
                ha='center', va='center', color='white', fontweight='bold')
    
    # Set title
    ax.set_title('Poverty Dimension Classification')
    
    # Remove axes and spines
    ax.set_xticks([])
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['bottom'].set_visible(False)
    ax.spines['left'].set_visible(False)
    
    plt.tight_layout()
    return fig

def plot_feature_importance(model, feature_names, title="Feature Importance"):
    """
    Plots the feature importances for a given model.
    Supports models with 'feature_importances_' or 'coef_' attributes.
    """
    import matplotlib.pyplot as plt
    import numpy as np
    if hasattr(model, 'feature_importances_'):
        importances = model.feature_importances_
    elif hasattr(model, 'coef_'):
        importances = np.abs(model.coef_)
    else:
        return None
    indices = np.argsort(importances)[::-1]
    top_n = min(10, len(feature_names))
    plt.figure(figsize=(8, 4))
    plt.title(title)
    plt.bar(range(top_n), importances[indices][:top_n], align="center")
    plt.xticks(range(top_n), [feature_names[i] for i in indices[:top_n]], rotation=45, ha='right')
    plt.tight_layout()
    return plt.gcf()