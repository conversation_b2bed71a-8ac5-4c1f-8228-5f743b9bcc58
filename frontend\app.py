"""
Modern Streamlit Frontend for Poverty Classification System
Production-ready interface with authentication and API integration
"""

import streamlit as st
import requests
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import json
import os
from typing import Dict, Any, Optional

# Page configuration
st.set_page_config(
    page_title="Poverty Classification System",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")

# Custom CSS
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #2a5298;
    }
    .prediction-result {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
        border: 1px solid #dee2e6;
        margin: 1rem 0;
    }
    .poor-indicator {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
        padding: 0.5rem;
        border-radius: 5px;
        margin: 0.25rem 0;
    }
    .non-poor-indicator {
        background: #d1edff;
        border: 1px solid #74b9ff;
        color: #0984e3;
        padding: 0.5rem;
        border-radius: 5px;
        margin: 0.25rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Session state initialization
if 'authenticated' not in st.session_state:
    st.session_state.authenticated = False
if 'user_info' not in st.session_state:
    st.session_state.user_info = None
if 'access_token' not in st.session_state:
    st.session_state.access_token = None

def make_api_request(endpoint: str, method: str = "GET", data: Dict = None, files: Dict = None) -> Optional[Dict]:
    """Make authenticated API request"""
    headers = {}
    if st.session_state.access_token:
        headers["Authorization"] = f"Bearer {st.session_state.access_token}"
    
    try:
        url = f"{API_BASE_URL}{endpoint}"
        
        if method == "GET":
            response = requests.get(url, headers=headers)
        elif method == "POST":
            if files:
                response = requests.post(url, headers=headers, files=files, data=data)
            else:
                headers["Content-Type"] = "application/json"
                response = requests.post(url, headers=headers, json=data)
        elif method == "PUT":
            headers["Content-Type"] = "application/json"
            response = requests.put(url, headers=headers, json=data)
        elif method == "DELETE":
            response = requests.delete(url, headers=headers)
        
        if response.status_code == 401:
            st.session_state.authenticated = False
            st.session_state.access_token = None
            st.error("Session expired. Please login again.")
            st.rerun()
        
        response.raise_for_status()
        return response.json()
    
    except requests.exceptions.RequestException as e:
        st.error(f"API Error: {str(e)}")
        return None

def login_page():
    """Login page"""
    st.markdown('<div class="main-header"><h1>🏠 Poverty Classification System</h1><p>Professional poverty analysis and classification platform</p></div>', unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown("### 🔐 Login")
        
        with st.form("login_form"):
            username = st.text_input("Username")
            password = st.text_input("Password", type="password")
            submit = st.form_submit_button("Login", use_container_width=True)
            
            if submit:
                if username and password:
                    # Attempt login
                    login_data = {"username": username, "password": password}
                    response = make_api_request("/api/v1/auth/login", "POST", login_data)
                    
                    if response:
                        st.session_state.authenticated = True
                        st.session_state.access_token = response["access_token"]
                        
                        # Get user info
                        user_info = make_api_request("/api/v1/auth/me")
                        if user_info:
                            st.session_state.user_info = user_info
                        
                        st.success("Login successful!")
                        st.rerun()
                else:
                    st.error("Please enter both username and password")
        
        st.markdown("---")
        st.markdown("### 📋 Demo Credentials")
        st.info("""
        **Admin:** username=`admin`, password=`admin123!`  
        **Analyst:** username=`analyst1`, password=`analyst123!`  
        **Viewer:** username=`viewer1`, password=`viewer123!`
        
        ⚠️ Change default passwords in production!
        """)

def main_app():
    """Main application interface"""
    # Header
    st.markdown('<div class="main-header"><h1>🏠 Poverty Classification System</h1><p>AI-Powered Poverty Analysis Platform</p></div>', unsafe_allow_html=True)
    
    # Sidebar
    with st.sidebar:
        st.markdown(f"### 👤 Welcome, {st.session_state.user_info['full_name'] or st.session_state.user_info['username']}")
        st.markdown(f"**Role:** {st.session_state.user_info['role'].title()}")
        
        if st.button("🚪 Logout"):
            make_api_request("/api/v1/auth/logout", "POST")
            st.session_state.authenticated = False
            st.session_state.access_token = None
            st.session_state.user_info = None
            st.rerun()
        
        st.markdown("---")
        
        # Navigation
        page = st.selectbox(
            "📋 Navigation",
            ["🏠 Dashboard", "🔮 Predict Poverty", "📊 Batch Analysis", "📈 Analytics", "👥 Admin Panel"]
        )
    
    # Main content based on selected page
    if page == "🏠 Dashboard":
        dashboard_page()
    elif page == "🔮 Predict Poverty":
        prediction_page()
    elif page == "📊 Batch Analysis":
        batch_analysis_page()
    elif page == "📈 Analytics":
        analytics_page()
    elif page == "👥 Admin Panel":
        admin_page()

def dashboard_page():
    """Dashboard with system overview"""
    st.markdown("## 📊 System Dashboard")
    
    # System health check
    health = make_api_request("/health")
    if health:
        if health["status"] == "healthy":
            st.success("✅ System Status: Operational")
        else:
            st.warning("⚠️ System Status: Issues Detected")
    
    # Quick stats (if admin/analyst)
    if st.session_state.user_info['role'] in ['admin', 'analyst']:
        stats = make_api_request("/api/v1/admin/stats")
        if stats:
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("Total Users", stats["total_users"])
            with col2:
                st.metric("Total Households", stats["total_households"])
            with col3:
                st.metric("Total Predictions", stats["total_predictions"])
            with col4:
                st.metric("Poverty Rate", f"{stats['income_poor_percentage']:.1f}%")
    
    # Recent activity
    st.markdown("### 📋 Recent Predictions")
    predictions = make_api_request("/api/v1/predictions/history?limit=10")
    if predictions:
        df = pd.DataFrame(predictions)
        if not df.empty:
            df['created_at'] = pd.to_datetime(df['created_at']).dt.strftime('%Y-%m-%d %H:%M')
            st.dataframe(df[['household_id', 'predicted_income', 'income_poor', 'multidimensionally_poor', 'created_at']], use_container_width=True)
        else:
            st.info("No predictions yet. Start by making your first prediction!")
    
    # Quick actions
    st.markdown("### ⚡ Quick Actions")
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🔮 New Prediction", use_container_width=True):
            st.session_state.page = "🔮 Predict Poverty"
            st.rerun()
    
    with col2:
        if st.button("📊 Batch Analysis", use_container_width=True):
            st.session_state.page = "📊 Batch Analysis"
            st.rerun()

def prediction_page():
    """Single household prediction page"""
    st.markdown("## 🔮 Household Poverty Prediction")
    
    with st.form("prediction_form"):
        st.markdown("### 📝 Household Information")
        
        # Create tabs for different categories
        demo_tab, edu_tab, health_tab, living_tab, economic_tab = st.tabs([
            "👥 Demographics", "🎓 Education", "🏥 Health", "🏠 Living Standards", "💰 Economic"
        ])
        
        # Demographics
        with demo_tab:
            col1, col2 = st.columns(2)
            with col1:
                household_size = st.number_input("Household Size", min_value=1, max_value=20, value=4)
                district = st.selectbox("District", [
                    "Colombo", "Gampaha", "Kalutara", "Kandy", "Matale", "Nuwara Eliya",
                    "Galle", "Matara", "Hambantota", "Jaffna", "Kilinochchi", "Mannar"
                ])
            with col2:
                num_employed = st.number_input("Number Employed", min_value=0, max_value=10, value=1)
                bus_halt_time = st.number_input("Time to Bus Halt (minutes)", min_value=0.0, value=15.0)
        
        # Education
        with edu_tab:
            col1, col2 = st.columns(2)
            with col1:
                education_level_max = st.number_input("Highest Education Level", min_value=0, max_value=20, value=10)
                education_years_mean = st.number_input("Average Years of Education", min_value=0.0, max_value=25.0, value=8.0)
            with col2:
                school_attendance_ratio = st.slider("School Attendance Ratio", 0.0, 1.0, 0.9)
                education_debt = st.number_input("Education Debt (LKR)", min_value=0.0, value=0.0)
        
        # Health
        with health_tab:
            col1, col2 = st.columns(2)
            with col1:
                chronic_illness = st.checkbox("Chronic Illness Present")
                health_insurance = st.checkbox("Has Health Insurance")
            with col2:
                disability = st.checkbox("Disability Present")
                hospital_time = st.number_input("Time to Hospital (minutes)", min_value=0.0, value=30.0)
                medical_debt = st.number_input("Medical Debt (LKR)", min_value=0.0, value=0.0)
        
        # Living Standards
        with living_tab:
            col1, col2 = st.columns(2)
            with col1:
                housing_quality_index = st.slider("Housing Quality Index", 0.0, 10.0, 5.0)
                adequate_sanitation = st.checkbox("Adequate Sanitation")
            with col2:
                good_drinking_water = st.checkbox("Good Drinking Water")
                weighted_durable_index = st.slider("Durable Goods Index", 0.0, 10.0, 5.0)
                housing_debt = st.number_input("Housing Debt (LKR)", min_value=0.0, value=0.0)
        
        # Economic
        with economic_tab:
            col1, col2 = st.columns(2)
            with col1:
                business_debt = st.number_input("Business Debt (LKR)", min_value=0.0, value=0.0)
                other_debt = st.number_input("Other Debt (LKR)", min_value=0.0, value=0.0)
            with col2:
                market_time = st.number_input("Time to Market (minutes)", min_value=0.0, value=20.0)
                school_time = st.number_input("Time to School (minutes)", min_value=0.0, value=25.0)
        
        # Submit button
        submitted = st.form_submit_button("🔮 Predict Poverty Status", use_container_width=True)
        
        if submitted:
            # Prepare prediction data
            prediction_data = {
                "household_size": household_size,
                "district": district,
                "education_level_max": education_level_max,
                "education_years_mean": education_years_mean,
                "school_attendance_ratio": school_attendance_ratio,
                "chronic_illness": chronic_illness,
                "health_insurance": health_insurance,
                "disability": disability,
                "hospital_time": hospital_time,
                "housing_quality_index": housing_quality_index,
                "adequate_sanitation": adequate_sanitation,
                "good_drinking_water": good_drinking_water,
                "weighted_durable_index": weighted_durable_index,
                "num_employed": num_employed,
                "bus_halt_time": bus_halt_time,
                "market_time": market_time,
                "school_time": school_time,
                "education_debt": education_debt,
                "medical_debt": medical_debt,
                "housing_debt": housing_debt,
                "business_debt": business_debt,
                "other_debt": other_debt
            }
            
            # Make prediction
            with st.spinner("🔮 Analyzing household data..."):
                result = make_api_request("/api/v1/predictions/predict", "POST", prediction_data)
                
                if result:
                    display_prediction_result(result)

def display_prediction_result(result: Dict[str, Any]):
    """Display prediction results"""
    st.markdown("## 📊 Prediction Results")
    
    # Income prediction
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric(
            "Predicted Monthly Income",
            f"LKR {result['predicted_income']:,.0f}",
            delta=None
        )
    
    with col2:
        poverty_status = "Poor" if result['income_poor'] else "Non-Poor"
        color = "red" if result['income_poor'] else "green"
        st.markdown(f"**Income Status:** <span style='color: {color}'>{poverty_status}</span>", unsafe_allow_html=True)
    
    with col3:
        st.metric(
            "Prediction Confidence",
            f"{result['confidence']*100:.1f}%"
        )
    
    # Poverty dimensions
    st.markdown("### 🎯 Poverty Dimensions Analysis")
    
    dimensions = {
        "Education": result['education_poor'],
        "Health": result['health_poor'],
        "Living Standards": result['living_standards_poor'],
        "Multidimensional": result['multidimensionally_poor']
    }
    
    cols = st.columns(len(dimensions))
    for i, (dimension, is_poor) in enumerate(dimensions.items()):
        with cols[i]:
            status = "Poor" if is_poor else "Non-Poor"
            css_class = "poor-indicator" if is_poor else "non-poor-indicator"
            st.markdown(f'<div class="{css_class}"><strong>{dimension}</strong><br>{status}</div>', unsafe_allow_html=True)
    
    # Recommendations
    if result.get('recommendations'):
        st.markdown("### 💡 Recommended Interventions")
        for category, recommendations in result['recommendations'].items():
            if recommendations:
                st.markdown(f"**{category.title()}:**")
                for rec in recommendations:
                    st.markdown(f"• {rec}")

def batch_analysis_page():
    """Batch analysis page"""
    st.markdown("## 📊 Batch Poverty Analysis")
    
    st.markdown("Upload a CSV or Excel file with household data for batch analysis.")
    
    uploaded_file = st.file_uploader(
        "Choose a file",
        type=['csv', 'xlsx', 'xls'],
        help="File should contain household data with appropriate columns"
    )
    
    if uploaded_file:
        if st.button("🚀 Start Batch Analysis"):
            with st.spinner("Processing batch analysis..."):
                files = {"file": uploaded_file}
                result = make_api_request("/api/v1/predictions/batch-predict", "POST", files=files)
                
                if result:
                    st.success(f"✅ Processed {result['total_predictions']} households successfully!")
                    
                    # Display summary
                    predictions_df = pd.DataFrame(result['predictions'])
                    
                    # Summary statistics
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Total Households", len(predictions_df))
                    with col2:
                        poverty_rate = (predictions_df['income_poor'].sum() / len(predictions_df)) * 100
                        st.metric("Income Poverty Rate", f"{poverty_rate:.1f}%")
                    with col3:
                        avg_income = predictions_df['predicted_income'].mean()
                        st.metric("Average Income", f"LKR {avg_income:,.0f}")
                    
                    # Results table
                    st.markdown("### 📋 Detailed Results")
                    st.dataframe(predictions_df, use_container_width=True)
                    
                    # Download results
                    csv = predictions_df.to_csv(index=False)
                    st.download_button(
                        "📥 Download Results",
                        csv,
                        "batch_analysis_results.csv",
                        "text/csv"
                    )

def analytics_page():
    """Analytics and reporting page"""
    st.markdown("## 📈 Analytics & Reports")
    
    # Get prediction history
    predictions = make_api_request("/api/v1/predictions/history?limit=100")
    
    if predictions and len(predictions) > 0:
        df = pd.DataFrame(predictions)
        df['created_at'] = pd.to_datetime(df['created_at'])
        
        # Time series chart
        st.markdown("### 📊 Predictions Over Time")
        daily_counts = df.groupby(df['created_at'].dt.date).size().reset_index()
        daily_counts.columns = ['Date', 'Predictions']
        
        fig = px.line(daily_counts, x='Date', y='Predictions', title='Daily Predictions')
        st.plotly_chart(fig, use_container_width=True)
        
        # Poverty distribution
        st.markdown("### 🎯 Poverty Distribution")
        col1, col2 = st.columns(2)
        
        with col1:
            poverty_dist = df['income_poor'].value_counts()
            fig = px.pie(values=poverty_dist.values, names=['Non-Poor', 'Poor'], title='Income Poverty Distribution')
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            multi_poverty_dist = df['multidimensionally_poor'].value_counts()
            fig = px.pie(values=multi_poverty_dist.values, names=['Non-Poor', 'Poor'], title='Multidimensional Poverty Distribution')
            st.plotly_chart(fig, use_container_width=True)
        
        # Income distribution
        st.markdown("### 💰 Income Distribution")
        fig = px.histogram(df, x='predicted_income', nbins=30, title='Predicted Income Distribution')
        st.plotly_chart(fig, use_container_width=True)
        
    else:
        st.info("No prediction data available for analytics. Make some predictions first!")

def admin_page():
    """Admin panel (admin only)"""
    if st.session_state.user_info['role'] != 'admin':
        st.error("🚫 Access denied. Admin privileges required.")
        return
    
    st.markdown("## 👥 Administration Panel")
    
    # System statistics
    stats = make_api_request("/api/v1/admin/stats")
    if stats:
        st.markdown("### 📊 System Statistics")
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Total Users", stats["total_users"])
            st.metric("Active Users", stats["active_users"])
        
        with col2:
            st.metric("Total Households", stats["total_households"])
            st.metric("Recent Households", stats["households_last_30_days"])
        
        with col3:
            st.metric("Total Predictions", stats["total_predictions"])
            st.metric("Recent Predictions", stats["predictions_last_30_days"])
        
        with col4:
            st.metric("Income Poverty Rate", f"{stats['income_poor_percentage']:.1f}%")
            st.metric("Multidimensional Poverty Rate", f"{stats['multidimensional_poor_percentage']:.1f}%")
    
    # User management
    st.markdown("### 👤 User Management")
    users = make_api_request("/api/v1/admin/users")
    if users:
        users_df = pd.DataFrame(users)
        users_df['created_at'] = pd.to_datetime(users_df['created_at']).dt.strftime('%Y-%m-%d')
        st.dataframe(users_df[['username', 'email', 'role', 'is_active', 'created_at']], use_container_width=True)

# Main application logic
def main():
    """Main application entry point"""
    if not st.session_state.authenticated:
        login_page()
    else:
        main_app()

if __name__ == "__main__":
    main()
