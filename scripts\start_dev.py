#!/usr/bin/env python3
"""
Development server startup script
Starts both API and frontend servers for development
"""

import os
import sys
import subprocess
import threading
import time
import signal
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import settings

class DevServer:
    def __init__(self):
        self.processes = []
        self.running = True
    
    def start_api_server(self):
        """Start FastAPI development server"""
        print("🚀 Starting API server...")
        cmd = [
            sys.executable, "-m", "uvicorn",
            "src.api.main:app",
            "--reload",
            "--host", settings.API_HOST,
            "--port", str(settings.API_PORT),
            "--log-level", settings.LOG_LEVEL.lower()
        ]
        
        process = subprocess.Popen(cmd, cwd=project_root)
        self.processes.append(("API", process))
        return process
    
    def start_frontend_server(self):
        """Start Streamlit frontend server"""
        print("🎨 Starting Frontend server...")
        cmd = [
            sys.executable, "-m", "streamlit", "run",
            "frontend/app.py",
            "--server.port", str(settings.FRONTEND_PORT),
            "--server.address", settings.FRONTEND_HOST,
            "--server.headless", "true",
            "--server.runOnSave", "true"
        ]
        
        process = subprocess.Popen(cmd, cwd=project_root)
        self.processes.append(("Frontend", process))
        return process
    
    def check_dependencies(self):
        """Check if required packages are installed"""
        print("🔍 Checking dependencies...")
        
        required_packages = [
            "fastapi", "uvicorn", "streamlit", "sqlalchemy", 
            "pandas", "scikit-learn", "pydantic"
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"❌ Missing packages: {', '.join(missing_packages)}")
            print("Please install dependencies: pip install -r requirements.txt")
            return False
        
        print("✅ All dependencies are installed")
        return True
    
    def setup_environment(self):
        """Setup development environment"""
        print("⚙️ Setting up development environment...")
        
        # Create necessary directories
        directories = ["logs", "uploads", "models"]
        for directory in directories:
            dir_path = project_root / directory
            dir_path.mkdir(exist_ok=True)
        
        # Set environment variables
        os.environ["ENVIRONMENT"] = "development"
        os.environ["DEBUG"] = "true"
        
        print("✅ Environment setup complete")
    
    def wait_for_servers(self):
        """Wait for servers to start and check health"""
        print("⏳ Waiting for servers to start...")
        
        import requests
        
        # Wait for API server
        api_url = f"http://{settings.API_HOST}:{settings.API_PORT}/health"
        for i in range(30):  # Wait up to 30 seconds
            try:
                response = requests.get(api_url, timeout=2)
                if response.status_code == 200:
                    print("✅ API server is ready")
                    break
            except requests.exceptions.RequestException:
                pass
            time.sleep(1)
        else:
            print("⚠️ API server may not be ready")
        
        # Wait for Frontend server
        frontend_url = f"http://{settings.FRONTEND_HOST}:{settings.FRONTEND_PORT}"
        for i in range(30):  # Wait up to 30 seconds
            try:
                response = requests.get(frontend_url, timeout=2)
                if response.status_code == 200:
                    print("✅ Frontend server is ready")
                    break
            except requests.exceptions.RequestException:
                pass
            time.sleep(1)
        else:
            print("⚠️ Frontend server may not be ready")
    
    def print_info(self):
        """Print server information"""
        print("\n" + "="*60)
        print("🎉 Development servers are running!")
        print("="*60)
        print(f"📡 API Server:      http://{settings.API_HOST}:{settings.API_PORT}")
        print(f"📚 API Docs:        http://{settings.API_HOST}:{settings.API_PORT}/docs")
        print(f"🎨 Frontend:        http://{settings.FRONTEND_HOST}:{settings.FRONTEND_PORT}")
        print(f"🔧 Environment:     {settings.ENVIRONMENT}")
        print("="*60)
        print("Press Ctrl+C to stop all servers")
        print("="*60 + "\n")
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print("\n🛑 Shutting down servers...")
        self.running = False
        
        for name, process in self.processes:
            print(f"Stopping {name} server...")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print(f"Force killing {name} server...")
                process.kill()
        
        print("✅ All servers stopped")
        sys.exit(0)
    
    def run(self):
        """Run development servers"""
        # Setup signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        try:
            # Check dependencies
            if not self.check_dependencies():
                sys.exit(1)
            
            # Setup environment
            self.setup_environment()
            
            # Start servers
            api_process = self.start_api_server()
            time.sleep(2)  # Give API server time to start
            
            frontend_process = self.start_frontend_server()
            time.sleep(3)  # Give frontend server time to start
            
            # Wait for servers to be ready
            self.wait_for_servers()
            
            # Print information
            self.print_info()
            
            # Keep running until interrupted
            while self.running:
                # Check if processes are still running
                for name, process in self.processes:
                    if process.poll() is not None:
                        print(f"❌ {name} server stopped unexpectedly")
                        self.running = False
                        break
                
                time.sleep(1)
        
        except KeyboardInterrupt:
            self.signal_handler(signal.SIGINT, None)
        except Exception as e:
            print(f"❌ Error starting development servers: {e}")
            sys.exit(1)


def main():
    """Main function"""
    print("🚀 Poverty Classification System - Development Server")
    print("=" * 60)
    
    server = DevServer()
    server.run()


if __name__ == "__main__":
    main()
