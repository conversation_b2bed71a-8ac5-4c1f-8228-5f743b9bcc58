# Poverty Classifier App

A machine learning-based system for classifying and analyzing poverty among low-income households. This app predicts household income, classifies poverty status, analyzes multidimensional poverty, and provides targeted recommendations.

## Features

- **Income Prediction:** Predicts household income using Linear Regression.
- **Poverty Threshold Classification:** Classifies households as poor or non-poor based on a configurable income threshold.
- **Multidimensional Poverty Analysis:** Analyzes education, health, and living standards for poor households.
- **Interactive Web Interface:** User-friendly data input and result visualization.
- **Recommendation System:** Offers targeted recommendations based on identified poverty dimensions.
- **Custom Dataset Support:** Upload your own preprocessed dataset to train the models.

## Technical Overview

- **Linear Regression Model:** Predicts household income from socioeconomic indicators.
- **Random Forest Classifiers:** Analyze specific poverty dimensions for poor households.

## Project Structure

```
app.py
models.py
requirements.txt
utils/
    utils.py
tests/
    test_app_ui.py
    test_models.py
test_results/
    test_app_ui_results.txt
    test_results.txt
```

## Installation

1. Clone the repository.
2. (Optional) Create and activate a virtual environment:
    ```bash
    python -m venv venv
    .\venv\Scripts\activate
    ```
3. Install dependencies:
    ```bash
    pip install -r requirements.txt
    ```

## Usage

1. Run the application:
    ```bash
    streamlit run app.py --server.port 5000
    ```
2. Open your browser and go to `http://localhost:5000`.
3. Upload your dataset or use the default.
4. Input household information and view predictions, analysis, and recommendations.

## Testing

Run tests using:
```bash
pytest tests/
```

## License

This project is for educational and research purposes.
