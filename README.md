# Poverty Classification System - Commercial Edition

A production-ready machine learning platform for comprehensive poverty analysis and classification. This enterprise-grade system provides accurate household income prediction, multidimensional poverty assessment, and actionable intervention recommendations for government agencies, NGOs, and research institutions.

## 🚀 Key Features

### Core Analytics
- **Advanced Income Prediction:** ML-powered income forecasting using ensemble models
- **Multidimensional Poverty Analysis:** Comprehensive assessment across education, health, and living standards
- **Real-time Classification:** Instant poverty status determination with configurable thresholds
- **Intervention Recommendations:** AI-driven, targeted intervention suggestions

### Enterprise Features
- **User Management:** Role-based access control (Admin, Analyst, Viewer)
- **Data Security:** End-to-end encryption and audit logging
- **Batch Processing:** Large-scale dataset analysis capabilities
- **API Integration:** RESTful APIs for system integration
- **Advanced Reporting:** Automated report generation and export
- **Model Management:** Version control and performance monitoring

### Technical Excellence
- **Scalable Architecture:** Microservices-based design
- **Database Integration:** PostgreSQL with data persistence
- **Monitoring & Alerting:** Real-time system health monitoring
- **CI/CD Pipeline:** Automated testing and deployment

## 🏗️ Architecture

```
poverty-classifier/
├── src/
│   ├── api/                    # FastAPI backend
│   ├── core/                   # Core business logic
│   ├── database/               # Database models and operations
│   ├── models/                 # ML models and training
│   ├── services/               # Business services
│   └── utils/                  # Utility functions
├── frontend/                   # Streamlit web interface
├── config/                     # Configuration files
├── tests/                      # Comprehensive test suite
├── docs/                       # Documentation
├── docker/                     # Docker configurations
└── scripts/                    # Deployment and utility scripts
```

## 🛠️ Technology Stack

- **Backend:** FastAPI, SQLAlchemy, PostgreSQL
- **Frontend:** Streamlit, React (optional)
- **ML/AI:** scikit-learn, pandas, numpy
- **Infrastructure:** Docker, Redis, Celery
- **Monitoring:** Prometheus, Grafana
- **Security:** JWT, bcrypt, SSL/TLS

## 📋 Prerequisites

- Python 3.9+
- PostgreSQL 12+
- Docker & Docker Compose
- Redis (for caching and task queue)

## 🚀 Quick Start

### Development Setup

1. **Clone and Setup Environment:**
   ```bash
   git clone <repository-url>
   cd poverty-classifier
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install Dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Database Setup:**
   ```bash
   # Start PostgreSQL with Docker
   docker-compose up -d postgres redis

   # Run database migrations
   python scripts/init_db.py
   ```

4. **Start Development Server:**
   ```bash
   # Start API server
   uvicorn src.api.main:app --reload --port 8000

   # Start Streamlit frontend (new terminal)
   streamlit run frontend/app.py --server.port 8501
   ```

### Production Deployment

```bash
# Build and deploy with Docker
docker-compose up -d

# Access the application
# Web Interface: http://localhost:8501
# API Documentation: http://localhost:8000/docs
```

## 📊 Usage

### Web Interface
1. Navigate to `http://localhost:8501`
2. Login with your credentials
3. Upload datasets or input household data
4. View comprehensive poverty analysis and recommendations

### API Integration
```python
import requests

# Predict household poverty
response = requests.post(
    "http://localhost:8000/api/v1/predict",
    json={"household_data": {...}},
    headers={"Authorization": "Bearer <token>"}
)
```

## 🧪 Testing

```bash
# Run all tests
pytest tests/ -v

# Run specific test categories
pytest tests/unit/ -v          # Unit tests
pytest tests/integration/ -v   # Integration tests
pytest tests/api/ -v           # API tests

# Generate coverage report
pytest --cov=src tests/
```

## 📈 Monitoring

- **Application Metrics:** http://localhost:3000 (Grafana)
- **API Health:** http://localhost:8000/health
- **System Logs:** Available via Docker logs

## 🔒 Security

- JWT-based authentication
- Role-based access control
- Data encryption at rest and in transit
- Comprehensive audit logging
- GDPR compliance features

## 📚 Documentation

- [User Guide](docs/user-guide.md)
- [API Documentation](docs/api.md)
- [Admin Guide](docs/admin-guide.md)
- [Development Guide](docs/development.md)

## 🤝 Support

For technical support and inquiries:
- Email: <EMAIL>
- Documentation: [docs.poverty-classifier.com](docs.poverty-classifier.com)
- Issue Tracker: GitHub Issues

## 📄 License

Commercial License - All rights reserved.
Contact for licensing inquiries: <EMAIL>

---

**Developed by Kalana Geethmal**
© 2024 All rights reserved. Unauthorized use or reproduction is prohibited.
