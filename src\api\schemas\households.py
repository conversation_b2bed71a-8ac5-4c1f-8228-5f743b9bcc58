"""
Household-related schemas
"""

from pydantic import BaseModel, validator
from typing import Optional, List, Dict, Any
from datetime import datetime


class HouseholdBase(BaseModel):
    """Base household schema"""
    household_id: Optional[str] = None
    household_size: int
    district: Optional[str] = None
    district_encoded: Optional[int] = None
    
    # Education data
    education_level_max: Optional[int] = None
    education_years_mean: Optional[float] = None
    school_attendance_ratio: Optional[float] = None
    education_debt: Optional[float] = 0.0
    
    # Health data
    chronic_illness: Optional[bool] = False
    health_insurance: Optional[bool] = False
    disability: Optional[bool] = False
    hospital_time: Optional[float] = None
    medical_debt: Optional[float] = 0.0
    
    # Living standards data
    housing_quality_index: Optional[float] = None
    adequate_sanitation: Optional[bool] = False
    good_drinking_water: Optional[bool] = False
    weighted_durable_index: Optional[float] = None
    housing_debt: Optional[float] = 0.0
    
    # Economic data
    num_employed: Optional[int] = 0
    business_debt: Optional[float] = 0.0
    other_debt: Optional[float] = 0.0
    total_debt: Optional[float] = 0.0
    
    # Accessibility data
    bus_halt_time: Optional[float] = None
    market_time: Optional[float] = None
    school_time: Optional[float] = None
    
    # Additional features
    additional_features: Optional[Dict[str, Any]] = None


class HouseholdCreate(HouseholdBase):
    """Schema for creating a household"""
    
    @validator('household_size')
    def validate_household_size(cls, v):
        if v < 1 or v > 20:
            raise ValueError('Household size must be between 1 and 20')
        return v
    
    @validator('education_level_max')
    def validate_education_level(cls, v):
        if v is not None and (v < 0 or v > 20):
            raise ValueError('Education level must be between 0 and 20')
        return v
    
    @validator('education_years_mean')
    def validate_education_years(cls, v):
        if v is not None and (v < 0 or v > 25):
            raise ValueError('Education years must be between 0 and 25')
        return v
    
    @validator('school_attendance_ratio')
    def validate_attendance_ratio(cls, v):
        if v is not None and (v < 0 or v > 1):
            raise ValueError('School attendance ratio must be between 0 and 1')
        return v
    
    @validator('housing_quality_index')
    def validate_housing_quality(cls, v):
        if v is not None and (v < 0 or v > 10):
            raise ValueError('Housing quality index must be between 0 and 10')
        return v
    
    @validator('weighted_durable_index')
    def validate_durable_index(cls, v):
        if v is not None and (v < 0 or v > 10):
            raise ValueError('Durable index must be between 0 and 10')
        return v
    
    @validator('num_employed')
    def validate_num_employed(cls, v):
        if v is not None and v < 0:
            raise ValueError('Number of employed cannot be negative')
        return v


class HouseholdUpdate(BaseModel):
    """Schema for updating a household"""
    household_size: Optional[int] = None
    district: Optional[str] = None
    district_encoded: Optional[int] = None
    
    # Education data
    education_level_max: Optional[int] = None
    education_years_mean: Optional[float] = None
    school_attendance_ratio: Optional[float] = None
    education_debt: Optional[float] = None
    
    # Health data
    chronic_illness: Optional[bool] = None
    health_insurance: Optional[bool] = None
    disability: Optional[bool] = None
    hospital_time: Optional[float] = None
    medical_debt: Optional[float] = None
    
    # Living standards data
    housing_quality_index: Optional[float] = None
    adequate_sanitation: Optional[bool] = None
    good_drinking_water: Optional[bool] = None
    weighted_durable_index: Optional[float] = None
    housing_debt: Optional[float] = None
    
    # Economic data
    num_employed: Optional[int] = None
    business_debt: Optional[float] = None
    other_debt: Optional[float] = None
    total_debt: Optional[float] = None
    
    # Accessibility data
    bus_halt_time: Optional[float] = None
    market_time: Optional[float] = None
    school_time: Optional[float] = None
    
    # Additional features
    additional_features: Optional[Dict[str, Any]] = None


class HouseholdResponse(HouseholdBase):
    """Schema for household response"""
    id: int
    created_by: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        orm_mode = True


class HouseholdList(BaseModel):
    """Schema for household list response"""
    households: List[HouseholdResponse]
    total: int
    limit: int
    offset: int
