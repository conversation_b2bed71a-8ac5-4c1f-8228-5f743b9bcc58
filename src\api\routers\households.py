"""
Household management endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime

from src.database.connection import get_db
from src.database.models import User, Household
from src.api.routers.auth import get_current_active_user, log_user_action
from src.api.schemas.households import (
    HouseholdCreate, HouseholdUpdate, HouseholdResponse, HouseholdList
)
from src.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.post("/", response_model=HouseholdResponse)
async def create_household(
    household_data: HouseholdCreate,
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new household record"""
    try:
        # Check if household_id already exists
        if household_data.household_id:
            existing = db.query(Household).filter(
                Household.household_id == household_data.household_id
            ).first()
            if existing:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Household ID already exists"
                )
        
        # Create household
        household = Household(
            **household_data.dict(),
            created_by=current_user.id
        )
        
        db.add(household)
        db.commit()
        db.refresh(household)
        
        # Log action
        await log_user_action(
            db, current_user, "create_household", request,
            resource_type="household", resource_id=household.household_id
        )
        
        logger.info(f"Household created: {household.household_id} by user {current_user.username}")
        
        return HouseholdResponse.from_orm(household)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create household: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create household"
        )


@router.get("/", response_model=HouseholdList)
async def list_households(
    limit: int = 50,
    offset: int = 0,
    district: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """List households with optional filtering"""
    try:
        query = db.query(Household)
        
        # Filter by district if provided
        if district:
            query = query.filter(Household.district == district)
        
        # Filter by user if not admin
        if current_user.role != "admin":
            query = query.filter(Household.created_by == current_user.id)
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        households = query.order_by(
            Household.created_at.desc()
        ).offset(offset).limit(limit).all()
        
        return HouseholdList(
            households=[HouseholdResponse.from_orm(h) for h in households],
            total=total,
            limit=limit,
            offset=offset
        )
        
    except Exception as e:
        logger.error(f"Failed to list households: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve households"
        )


@router.get("/{household_id}", response_model=HouseholdResponse)
async def get_household(
    household_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get household details by ID"""
    try:
        query = db.query(Household).filter(Household.household_id == household_id)
        
        # Filter by user if not admin
        if current_user.role != "admin":
            query = query.filter(Household.created_by == current_user.id)
        
        household = query.first()
        
        if not household:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Household not found"
            )
        
        return HouseholdResponse.from_orm(household)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get household: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve household"
        )


@router.put("/{household_id}", response_model=HouseholdResponse)
async def update_household(
    household_id: str,
    household_update: HouseholdUpdate,
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update household information"""
    try:
        query = db.query(Household).filter(Household.household_id == household_id)
        
        # Filter by user if not admin
        if current_user.role != "admin":
            query = query.filter(Household.created_by == current_user.id)
        
        household = query.first()
        
        if not household:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Household not found"
            )
        
        # Update fields
        update_data = household_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(household, field, value)
        
        household.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(household)
        
        # Log action
        await log_user_action(
            db, current_user, "update_household", request,
            resource_type="household", resource_id=household.household_id,
            request_data=update_data
        )
        
        logger.info(f"Household updated: {household.household_id} by user {current_user.username}")
        
        return HouseholdResponse.from_orm(household)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update household: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update household"
        )


@router.delete("/{household_id}")
async def delete_household(
    household_id: str,
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete household record"""
    try:
        query = db.query(Household).filter(Household.household_id == household_id)
        
        # Only admin or owner can delete
        if current_user.role != "admin":
            query = query.filter(Household.created_by == current_user.id)
        
        household = query.first()
        
        if not household:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Household not found"
            )
        
        db.delete(household)
        db.commit()
        
        # Log action
        await log_user_action(
            db, current_user, "delete_household", request,
            resource_type="household", resource_id=household_id
        )
        
        logger.info(f"Household deleted: {household_id} by user {current_user.username}")
        
        return {"message": "Household deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete household: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete household"
        )
