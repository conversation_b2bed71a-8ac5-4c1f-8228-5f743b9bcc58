"""
Prediction endpoints for poverty classification
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request, UploadFile, File
from sqlalchemy.orm import Session
from typing import List, Optional
import pandas as pd
import numpy as np
from datetime import datetime
import uuid

from src.database.connection import get_db
from src.database.models import User, Household, Prediction
from src.api.routers.auth import get_current_active_user, log_user_action
from src.api.schemas.predictions import (
    HouseholdInput, PredictionResponse, BatchPredictionRequest,
    BatchPredictionResponse, PredictionHistory
)
from src.services.prediction_service import PredictionService
from src.core.logging import get_logger
from config.settings import settings

logger = get_logger(__name__)
router = APIRouter()


@router.post("/predict", response_model=PredictionResponse)
async def predict_household_poverty(
    household_data: HouseholdInput,
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Predict poverty status for a single household
    """
    try:
        # Initialize prediction service
        prediction_service = PredictionService()
        
        # Convert input to DataFrame
        input_df = pd.DataFrame([household_data.dict()])
        
        # Make prediction
        prediction_result = await prediction_service.predict_single(input_df)
        
        # Save household data if it doesn't exist
        household = Household(
            household_id=str(uuid.uuid4()),
            household_size=household_data.household_size,
            district=household_data.district,
            education_level_max=household_data.education_level_max,
            education_years_mean=household_data.education_years_mean,
            school_attendance_ratio=household_data.school_attendance_ratio,
            chronic_illness=household_data.chronic_illness,
            health_insurance=household_data.health_insurance,
            disability=household_data.disability,
            hospital_time=household_data.hospital_time,
            housing_quality_index=household_data.housing_quality_index,
            adequate_sanitation=household_data.adequate_sanitation,
            good_drinking_water=household_data.good_drinking_water,
            weighted_durable_index=household_data.weighted_durable_index,
            num_employed=household_data.num_employed,
            bus_halt_time=household_data.bus_halt_time,
            market_time=household_data.market_time,
            school_time=household_data.school_time,
            education_debt=household_data.education_debt or 0.0,
            medical_debt=household_data.medical_debt or 0.0,
            housing_debt=household_data.housing_debt or 0.0,
            business_debt=household_data.business_debt or 0.0,
            other_debt=household_data.other_debt or 0.0,
            created_by=current_user.id
        )
        
        db.add(household)
        db.commit()
        db.refresh(household)
        
        # Save prediction result
        prediction = Prediction(
            prediction_id=str(uuid.uuid4()),
            household_id=household.id,
            created_by=current_user.id,
            predicted_income=prediction_result["predicted_income"],
            income_poor=prediction_result["income_poor"],
            education_poor=prediction_result["education_poor"],
            health_poor=prediction_result["health_poor"],
            living_standards_poor=prediction_result["living_standards_poor"],
            multidimensionally_poor=prediction_result["multidimensionally_poor"],
            education_poor_probability=prediction_result.get("education_poor_probability"),
            health_poor_probability=prediction_result.get("health_poor_probability"),
            living_standards_poor_probability=prediction_result.get("living_standards_poor_probability"),
            multidimensional_poor_probability=prediction_result.get("multidimensional_poor_probability"),
            model_version=prediction_result.get("model_version", "1.0.0"),
            poverty_threshold=prediction_result.get("poverty_threshold", settings.DEFAULT_POVERTY_THRESHOLD),
            prediction_confidence=prediction_result.get("confidence", 0.0),
            recommendations=prediction_result.get("recommendations", {})
        )
        
        db.add(prediction)
        db.commit()
        db.refresh(prediction)
        
        # Log the prediction action
        await log_user_action(
            db, current_user, "predict", request,
            resource_type="prediction", resource_id=prediction.prediction_id,
            request_data={"household_id": household.household_id}
        )
        
        logger.info(f"Prediction completed for user {current_user.username}")
        
        return PredictionResponse(
            prediction_id=prediction.prediction_id,
            household_id=household.household_id,
            predicted_income=prediction.predicted_income,
            income_poor=prediction.income_poor,
            education_poor=prediction.education_poor,
            health_poor=prediction.health_poor,
            living_standards_poor=prediction.living_standards_poor,
            multidimensionally_poor=prediction.multidimensionally_poor,
            probabilities={
                "education_poor": prediction.education_poor_probability,
                "health_poor": prediction.health_poor_probability,
                "living_standards_poor": prediction.living_standards_poor_probability,
                "multidimensional_poor": prediction.multidimensional_poor_probability
            },
            confidence=prediction.prediction_confidence,
            recommendations=prediction.recommendations,
            model_version=prediction.model_version,
            poverty_threshold=prediction.poverty_threshold,
            created_at=prediction.created_at
        )
        
    except Exception as e:
        logger.error(f"Prediction failed for user {current_user.username}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Prediction failed: {str(e)}"
        )


@router.post("/batch-predict", response_model=BatchPredictionResponse)
async def batch_predict_poverty(
    file: UploadFile = File(...),
    request: Request = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Batch prediction for multiple households from uploaded file
    """
    try:
        # Validate file type
        if not file.filename.endswith(('.csv', '.xlsx', '.xls')):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only CSV and Excel files are supported"
            )
        
        # Read file
        if file.filename.endswith('.csv'):
            df = pd.read_csv(file.file)
        else:
            df = pd.read_excel(file.file)
        
        # Validate file size
        if len(df) > 1000:  # Limit batch size
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Batch size limited to 1000 households"
            )
        
        # Initialize prediction service
        prediction_service = PredictionService()
        
        # Make batch predictions
        predictions = await prediction_service.predict_batch(df)
        
        # Save results to database
        saved_predictions = []
        for idx, prediction_result in enumerate(predictions):
            # Create household record
            household_data = df.iloc[idx].to_dict()
            household = Household(
                household_id=household_data.get('household_id', str(uuid.uuid4())),
                created_by=current_user.id,
                **{k: v for k, v in household_data.items() if hasattr(Household, k)}
            )
            
            db.add(household)
            db.commit()
            db.refresh(household)
            
            # Create prediction record
            prediction = Prediction(
                prediction_id=str(uuid.uuid4()),
                household_id=household.id,
                created_by=current_user.id,
                **prediction_result
            )
            
            db.add(prediction)
            saved_predictions.append(prediction)
        
        db.commit()
        
        # Log batch prediction
        await log_user_action(
            db, current_user, "batch_predict", request,
            resource_type="batch_prediction",
            request_data={"file_name": file.filename, "batch_size": len(df)}
        )
        
        logger.info(f"Batch prediction completed for {len(df)} households by user {current_user.username}")
        
        return BatchPredictionResponse(
            batch_id=str(uuid.uuid4()),
            total_predictions=len(saved_predictions),
            successful_predictions=len(saved_predictions),
            failed_predictions=0,
            predictions=[
                PredictionResponse.from_orm(pred) for pred in saved_predictions
            ]
        )
        
    except Exception as e:
        logger.error(f"Batch prediction failed for user {current_user.username}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Batch prediction failed: {str(e)}"
        )


@router.get("/history", response_model=List[PredictionHistory])
async def get_prediction_history(
    limit: int = 50,
    offset: int = 0,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get prediction history for the current user
    """
    try:
        predictions = db.query(Prediction).filter(
            Prediction.created_by == current_user.id
        ).order_by(
            Prediction.created_at.desc()
        ).offset(offset).limit(limit).all()
        
        return [
            PredictionHistory(
                prediction_id=pred.prediction_id,
                household_id=pred.household.household_id,
                predicted_income=pred.predicted_income,
                income_poor=pred.income_poor,
                multidimensionally_poor=pred.multidimensionally_poor,
                created_at=pred.created_at
            )
            for pred in predictions
        ]
        
    except Exception as e:
        logger.error(f"Failed to get prediction history for user {current_user.username}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve prediction history"
        )


@router.get("/{prediction_id}", response_model=PredictionResponse)
async def get_prediction_details(
    prediction_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get detailed prediction results by ID
    """
    try:
        prediction = db.query(Prediction).filter(
            Prediction.prediction_id == prediction_id,
            Prediction.created_by == current_user.id
        ).first()
        
        if not prediction:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Prediction not found"
            )
        
        return PredictionResponse.from_orm(prediction)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get prediction details: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve prediction details"
        )
