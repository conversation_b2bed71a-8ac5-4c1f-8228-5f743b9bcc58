"""
Database models for Poverty Classification System
"""

from sqlalchemy import (
    Column, Integer, String, Float, Boolean, DateTime, Text, 
    ForeignKey, JSON, Index, UniqueConstraint
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
import uuid

from .connection import Base


class User(Base):
    """User model for authentication and authorization"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100))
    role = Column(String(20), default="viewer")  # admin, analyst, viewer
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True))
    
    # Relationships
    households = relationship("Household", back_populates="created_by_user")
    predictions = relationship("Prediction", back_populates="created_by_user")
    audit_logs = relationship("AuditLog", back_populates="user")


class Household(Base):
    """Household data model"""
    __tablename__ = "households"
    
    id = Column(Integer, primary_key=True, index=True)
    household_id = Column(String(50), unique=True, index=True)  # External ID
    
    # Demographic data
    household_size = Column(Integer)
    district = Column(String(100))
    district_encoded = Column(Integer)
    
    # Education data
    education_level_max = Column(Integer)
    education_years_mean = Column(Float)
    school_attendance_ratio = Column(Float)
    education_debt = Column(Float, default=0.0)
    
    # Health data
    chronic_illness = Column(Boolean, default=False)
    health_insurance = Column(Boolean, default=False)
    disability = Column(Boolean, default=False)
    hospital_time = Column(Float)
    medical_debt = Column(Float, default=0.0)
    
    # Living standards data
    housing_quality_index = Column(Float)
    adequate_sanitation = Column(Boolean, default=False)
    good_drinking_water = Column(Boolean, default=False)
    weighted_durable_index = Column(Float)
    housing_debt = Column(Float, default=0.0)
    
    # Economic data
    num_employed = Column(Integer, default=0)
    business_debt = Column(Float, default=0.0)
    other_debt = Column(Float, default=0.0)
    total_debt = Column(Float, default=0.0)
    
    # Accessibility data
    bus_halt_time = Column(Float)
    market_time = Column(Float)
    school_time = Column(Float)
    
    # Additional features (JSON for flexibility)
    additional_features = Column(JSON)
    
    # Metadata
    created_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    created_by_user = relationship("User", back_populates="households")
    predictions = relationship("Prediction", back_populates="household")
    
    # Indexes
    __table_args__ = (
        Index('idx_household_district', 'district'),
        Index('idx_household_created_at', 'created_at'),
    )


class Prediction(Base):
    """Prediction results model"""
    __tablename__ = "predictions"
    
    id = Column(Integer, primary_key=True, index=True)
    prediction_id = Column(String(50), unique=True, index=True, default=lambda: str(uuid.uuid4()))
    
    # Foreign keys
    household_id = Column(Integer, ForeignKey("households.id"))
    created_by = Column(Integer, ForeignKey("users.id"))
    
    # Prediction results
    predicted_income = Column(Float)
    income_poor = Column(Boolean)
    education_poor = Column(Boolean)
    health_poor = Column(Boolean)
    living_standards_poor = Column(Boolean)
    multidimensionally_poor = Column(Boolean)
    
    # Prediction probabilities
    education_poor_probability = Column(Float)
    health_poor_probability = Column(Float)
    living_standards_poor_probability = Column(Float)
    multidimensional_poor_probability = Column(Float)
    
    # Model information
    model_version = Column(String(50))
    poverty_threshold = Column(Float)
    
    # Confidence scores
    prediction_confidence = Column(Float)
    
    # Recommendations (JSON)
    recommendations = Column(JSON)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    household = relationship("Household", back_populates="predictions")
    created_by_user = relationship("User", back_populates="predictions")
    
    # Indexes
    __table_args__ = (
        Index('idx_prediction_created_at', 'created_at'),
        Index('idx_prediction_income_poor', 'income_poor'),
        Index('idx_prediction_multidimensional', 'multidimensionally_poor'),
    )


class ModelVersion(Base):
    """Model version tracking"""
    __tablename__ = "model_versions"
    
    id = Column(Integer, primary_key=True, index=True)
    version = Column(String(50), unique=True, index=True)
    model_type = Column(String(50))  # income, education_poor, etc.
    
    # Model metadata
    algorithm = Column(String(100))
    hyperparameters = Column(JSON)
    training_data_size = Column(Integer)
    
    # Performance metrics
    accuracy = Column(Float)
    precision = Column(Float)
    recall = Column(Float)
    f1_score = Column(Float)
    r2_score = Column(Float)
    rmse = Column(Float)
    
    # Model file information
    model_path = Column(String(255))
    model_size_bytes = Column(Integer)
    
    # Status
    is_active = Column(Boolean, default=False)
    is_production = Column(Boolean, default=False)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    created_by = Column(Integer, ForeignKey("users.id"))
    
    # Indexes
    __table_args__ = (
        Index('idx_model_version_type', 'model_type'),
        Index('idx_model_version_active', 'is_active'),
    )


class AuditLog(Base):
    """Audit log for tracking user actions"""
    __tablename__ = "audit_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # User and action information
    user_id = Column(Integer, ForeignKey("users.id"))
    action = Column(String(100), nullable=False)  # login, predict, upload, etc.
    resource_type = Column(String(50))  # household, prediction, model, etc.
    resource_id = Column(String(50))
    
    # Request details
    ip_address = Column(String(45))  # IPv6 compatible
    user_agent = Column(Text)
    request_data = Column(JSON)
    
    # Response details
    status_code = Column(Integer)
    response_data = Column(JSON)
    
    # Timing
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    duration_ms = Column(Integer)
    
    # Relationships
    user = relationship("User", back_populates="audit_logs")
    
    # Indexes
    __table_args__ = (
        Index('idx_audit_user_id', 'user_id'),
        Index('idx_audit_action', 'action'),
        Index('idx_audit_timestamp', 'timestamp'),
        Index('idx_audit_resource', 'resource_type', 'resource_id'),
    )


class SystemMetrics(Base):
    """System performance metrics"""
    __tablename__ = "system_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Metric information
    metric_name = Column(String(100), nullable=False)
    metric_value = Column(Float, nullable=False)
    metric_unit = Column(String(20))
    
    # Additional data
    tags = Column(JSON)  # For grouping and filtering
    
    # Timing
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    
    # Indexes
    __table_args__ = (
        Index('idx_metrics_name_timestamp', 'metric_name', 'timestamp'),
    )
