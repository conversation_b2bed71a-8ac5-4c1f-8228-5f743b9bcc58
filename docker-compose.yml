version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: poverty_classifier_db
    environment:
      POSTGRES_DB: poverty_classifier
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: poverty_classifier_redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # FastAPI Backend
  api:
    build:
      context: .
      target: production
    container_name: poverty_classifier_api
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=********************************************/poverty_classifier
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-production-secret-key-change-this
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./models:/app/models
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    command: ["uvicorn", "src.api.main:app", "--host", "0.0.0.0", "--port", "8000"]

  # Streamlit Frontend
  frontend:
    build:
      context: .
      target: production
    container_name: poverty_classifier_frontend
    environment:
      - ENVIRONMENT=production
      - API_BASE_URL=http://api:8000
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    ports:
      - "8501:8501"
    depends_on:
      - api
    restart: unless-stopped
    command: ["streamlit", "run", "frontend/app.py", "--server.port", "8501", "--server.address", "0.0.0.0"]

  # Celery Worker (for background tasks)
  worker:
    build:
      context: .
      target: production
    container_name: poverty_classifier_worker
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=********************************************/poverty_classifier
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./logs:/app/logs
      - ./models:/app/models
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: ["celery", "-A", "src.services.tasks", "worker", "--loglevel=info"]

  # Celery Beat (for scheduled tasks)
  scheduler:
    build:
      context: .
      target: production
    container_name: poverty_classifier_scheduler
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=********************************************/poverty_classifier
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: ["celery", "-A", "src.services.tasks", "beat", "--loglevel=info"]

  # Nginx Reverse Proxy (optional, for production)
  nginx:
    image: nginx:alpine
    container_name: poverty_classifier_nginx
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - api
      - frontend
    restart: unless-stopped
    profiles:
      - production

  # Prometheus Monitoring (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: poverty_classifier_prometheus
    volumes:
      - ./docker/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana Dashboard (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: poverty_classifier_grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  default:
    name: poverty_classifier_network
