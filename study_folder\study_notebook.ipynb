{"cells": [{"cell_type": "markdown", "id": "6b32094f", "metadata": {}, "source": ["# Streamlit App: Sri Lanka HIES-based Poverty Analysis System\n", "This notebook contains the main logic and code sections from the app.py file, organized for study and experimentation."]}, {"cell_type": "markdown", "id": "1206dab4", "metadata": {}, "source": ["## Imports and Initial Setup\n", "Import all required libraries, set up Streamlit configuration, and load custom CSS if available."]}, {"cell_type": "code", "execution_count": null, "id": "e12ee717", "metadata": {}, "outputs": [], "source": ["import streamlit as st\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split\n", "from models import create_income_model, create_poverty_dimension_model, evaluate_income_model, evaluate_poverty_dimension_model, train_all_models, get_recommended_interventions\n", "from utils.utils import generate_sample_data, prepare_features, load_and_prepare_data, plot_income_prediction, plot_poverty_dimensions, plot_feature_importance, engineer_hies_features\n", "import os\n", "\n", "# Set page configuration\n", "st.set_page_config(page_title=\"SL HIES Poverty Analysis\", page_icon=\"📊\", layout=\"wide\")\n", "\n", "# Apply custom CSS styling\n", "def load_css(css_file):\n", "    with open(css_file, 'r') as f:\n", "        css = f.read()\n", "    st.markdown(f'<style>{css}</style>', unsafe_allow_html=True)\n", "\n", "# Load custom CSS if available\n", "if os.path.exists('style.css'):\n", "    load_css('style.css')"]}, {"cell_type": "markdown", "id": "ad7bbdec", "metadata": {}, "source": ["## Constants and Session State Initialization\n", "Define default thresholds, target columns, and initialize Streamlit session state variables."]}, {"cell_type": "code", "execution_count": null, "id": "b4d2df4a", "metadata": {}, "outputs": [], "source": ["DEFAULT_POVERTY_THRESHOLD = 50000  # Default threshold in LKR\n", "TARGET_COLUMNS = ['hhincomepm', 'income_poor', 'education_poor', 'health_poor', 'living_standards_poor', 'multidimensionally_poor']\n", "\n", "DEFAULT_THRESHOLDS = {\n", "    'health': 0.5,\n", "    'education': 0.5,\n", "    'living_standards': 0.5,\n", "    'multidimensional': 0.5\n", "}\n", "\n", "for key, value in DEFAULT_THRESHOLDS.items():\n", "    session_key = f'{key}_poverty_threshold'\n", "    if session_key not in st.session_state:\n", "        st.session_state[session_key] = value\n", "\n", "# Initialize session state variables if they don't exist\n", "if 'models_trained' not in st.session_state:\n", "    st.session_state.models_trained = False\n", "if 'using_custom_data' not in st.session_state:\n", "    st.session_state.using_custom_data = False\n", "if 'data_uploaded' not in st.session_state:\n", "    st.session_state.data_uploaded = False\n", "if 'threshold' not in st.session_state:\n", "    st.session_state.threshold = DEFAULT_POVERTY_THRESHOLD\n", "if 'models' not in st.session_state:\n", "    st.session_state.models = {}\n", "if 'metrics' not in st.session_state:\n", "    st.session_state.metrics = {}\n", "if 'poverty_dimension_models' not in st.session_state:\n", "    st.session_state.poverty_dimension_models = {}"]}, {"cell_type": "markdown", "id": "ab9d4ee3", "metadata": {}, "source": ["## App Title and Introduction\n", "Display the main title and a brief description of the app's purpose."]}, {"cell_type": "code", "execution_count": null, "id": "12a79524", "metadata": {}, "outputs": [], "source": ["st.title(\"Sri Lanka HIES-based Poverty Analysis System\")\n", "st.markdown(\"\"\"\n", "This application predicts household income using Linear Regression and classifies \\npoverty dimensions using Random Forest classifiers. Upload your HIES 2019 dataset \\nor input household information to receive predictions on income level and poverty classification.\n", "\"\"\")"]}, {"cell_type": "markdown", "id": "9b19d736", "metadata": {}, "source": ["## Data Uploading and Model Training Section\n", "This section allows users to upload their HIES dataset, view data quality, and train machine learning models for poverty analysis."]}, {"cell_type": "code", "execution_count": null, "id": "869bdd76", "metadata": {}, "outputs": [], "source": ["with st.expander(\"📥 Upload Dataset & Train Models\", expanded=not st.session_state.models_trained):\n", "    st.markdown(\"\"\"\n", "    ### Upload Your HIES Dataset\n", "    Upload a CSV or Excel file containing your preprocessed household data from the Sri Lanka HIES 2019 survey.\n", "    The file should include features like: ...\n", "    If target columns are missing, they will be derived from available features.\n", "    \"\"\")\n", "    uploaded_file = st.file_uploader(\"Choose a CSV or Excel file\", type=[\"csv\", \"xlsx\", \"xls\"])\n", "    if uploaded_file is not None:\n", "        try:\n", "            with st.spinner(\"Processing your dataset...\"):\n", "                X, y_dict = load_and_prepare_data(uploaded_file, TARGET_COLUMNS)\n", "                st.session_state.X = X\n", "                st.session_state.y_dict = y_dict\n", "                st.session_state.data_uploaded = True\n", "                # ... (data quality checks and display) ...\n", "        except Exception as e:\n", "            st.error(f\"Error loading dataset: {str(e)}\")\n", "    # ... (model training UI and logic) ..."]}, {"cell_type": "markdown", "id": "06e1d3e1", "metadata": {}, "source": ["## Sidebar: Model Parameters and Information\n", "The sidebar provides information about the system, current thresholds, and allows users to adjust model parameters interactively."]}, {"cell_type": "code", "execution_count": null, "id": "dc2a864c", "metadata": {}, "outputs": [], "source": ["with st.sidebar:\n", "    st.header(\"About the System\")\n", "    if st.session_state.using_custom_data:\n", "        st.markdown(\"\"\"\n", "This system is currently using **your uploaded HIES dataset** to classify households based on: ...\n", "\"\"\")\n", "    else:\n", "        st.markdown(\"\"\"\n", "This system uses machine learning to classify households based on: ...\n", "\"\"\")\n", "    st.markdown(f\"Current poverty threshold: LKR {st.session_state.threshold:,}\")\n", "    if st.session_state.models_trained:\n", "        st.header(\"Parameters\")\n", "        custom_threshold = st.number_input(\"Custom Poverty Threshold (LKR)\", min_value=10000, max_value=200000, value=st.session_state.threshold, step=5000)\n", "        # ... (poverty dimension threshold sliders and guidance) ..."]}, {"cell_type": "markdown", "id": "b4081bbc", "metadata": {}, "source": ["## Household Information Input Interface\n", "This section provides an interactive form for users to input household demographic, education, health, living standards, and economic information for prediction."]}, {"cell_type": "code", "execution_count": null, "id": "2bb3fc5a", "metadata": {}, "outputs": [], "source": ["# Create tabs for different category inputs\n", "demo_tab, edu_tab, health_tab, living_tab, economic_tab = st.tabs([\"Demographic\", \"Education\", \"Health\", \"Living Standards\", \"Economic\"])\n", "\n", "# Demographic Information tab\n", "with demo_tab:\n", "    st.subheader(\"Demographic Information\")\n", "    col1, col2 = st.columns(2)\n", "    with col1:\n", "        hhsize = st.number_input(\"Household Size (Number of People)\", min_value=1, max_value=15, value=4)\n", "        house_size_sqft = st.number_input(\"House Size (Square Feet)\", min_value=100, max_value=10000, value=800, help=\"Size of the dwelling in square feet\")\n", "        sqft_per_person = house_size_sqft / max(1, hhsize)\n", "    with col2:\n", "        district = st.selectbox(\"District\", options=[\"Colombo\", ...], index=0)\n", "        sector = st.selectbox(\"Sector\", options=[\"Urban\", \"Rural\", \"Estate\"], index=1)\n", "        st.metric(\"Square Feet per Person\", f\"{sqft_per_person:.1f} sq.ft.\", help=\"Living space per household member. Values below 100 sq.ft. per person indicate possible overcrowding.\")\n", "# ... (other tabs for education, health, living standards, economic) ..."]}, {"cell_type": "markdown", "id": "dcc0a1ed", "metadata": {}, "source": ["## Prediction and Results Display\n", "This section prepares the input data, runs the trained models, and displays the results for income and poverty dimension predictions."]}, {"cell_type": "code", "execution_count": null, "id": "2a53298d", "metadata": {}, "outputs": [], "source": ["if not st.session_state.models_trained:\n", "    st.error(\"Please train the models first by uploading a dataset or using the default models.\")\n", "else:\n", "    # Prepare input data\n", "    input_data = { ... }  # See app.py for full details\n", "    input_df = pd.DataFrame([input_data])\n", "    engineered_df = engineer_hies_features(input_df)\n", "    prepared_df = prepare_features(engineered_df)\n", "    try:\n", "        # Predict income if model exists\n", "        if 'hhincomepm' in st.session_state.models:\n", "            # ... (prediction logic) ...\n", "            predicted_income = st.session_state.models['hhincomepm'].predict(prepared_df)[0]\n", "        else:\n", "            predicted_income = engineered_df['hhincomepm'].iloc[0] if 'hhincomepm' in engineered_df.columns else 45000\n", "        income_poor = predicted_income < custom_threshold\n", "        dimension_results = {'income_poor': int(income_poor)}\n", "        # Predict other poverty dimensions\n", "        for dimension in ['education_poor', 'health_poor', 'living_standards_poor', 'multidimensionally_poor']:\n", "            # ... (dimension prediction logic) ...\n", "            pass\n", "        # Display results\n", "        st.header(\"Analysis Results\")\n", "        st.subheader(\"Income Prediction\")\n", "        st.metric(\"Predicted Monthly Household Income\", f\"LKR {predicted_income:,.2f}\")\n", "        fig_income = plot_income_prediction(predicted_income, st.session_state.threshold)\n", "        st.pyplot(fig_income)\n", "        is_poor = predicted_income < st.session_state.threshold\n", "        if is_poor:\n", "            st.error(f\"⚠️ This household is classified as INCOME POOR (below threshold of LKR {st.session_state.threshold:,})\")\n", "        else:\n", "            st.success(f\"✅ This household is NOT INCOME POOR (above threshold of LKR {st.session_state.threshold:,})\")\n", "        st.subheader(\"Poverty Classification by Dimension:\")\n", "        for dimension, result in dimension_results.items():\n", "            if result:\n", "                st.error(f\"❌ {dimension.replace('_', ' ').title()} Poverty: POOR\")\n", "            else:\n", "                st.success(f\"✅ {dimension.replace('_', ' ').title()} Poverty: NOT POOR\")\n", "    except Exception as e:\n", "        st.error(\"Error generating predictions. Please check your input data.\")\n", "        st.exception(e)"]}, {"cell_type": "markdown", "id": "477d3513", "metadata": {}, "source": ["---\n", "# models.py: Machine Learning Model Functions\n", "This section contains all model creation, evaluation, and recommendation functions used in the app."]}, {"cell_type": "code", "execution_count": null, "id": "86f1a305", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor\n", "from sklearn.model_selection import cross_val_score, KFold\n", "from sklearn.metrics import mean_squared_error, r2_score, accuracy_score, precision_score, recall_score, f1_score, classification_report, roc_auc_score\n", "\n", "def create_income_model(X, y):\n", "    model = RandomForestRegressor(n_estimators=100, max_depth=None, min_samples_split=2, min_samples_leaf=1, random_state=42)\n", "    y_train = np.maximum(y, 0)\n", "    model.fit(X, y_train)\n", "    class PositiveIncomeModel:\n", "        def __init__(self, base_model):\n", "            self.base_model = base_model\n", "        def predict(self, X):\n", "            preds = self.base_model.predict(X)\n", "            return np.maximum(preds, 10000)\n", "        def __getattr__(self, attr):\n", "            return getattr(self.base_model, attr)\n", "    return PositiveIncomeModel(model)\n", "\n", "def create_poverty_dimension_model(X, y):\n", "    model = RandomForestClassifier(n_estimators=100, max_depth=10, min_samples_split=5, min_samples_leaf=2, random_state=42, class_weight='balanced')\n", "    model.fit(X, y)\n", "    return model\n", "\n", "def evaluate_income_model(model, X, y):\n", "    y_pred = model.predict(X)\n", "    mse = mean_squared_error(y, y_pred)\n", "    rmse = np.sqrt(mse)\n", "    r2 = r2_score(y, y_pred)\n", "    mape = np.mean(np.abs((y - y_pred) / np.maximum(y, 1))) * 100\n", "    cv = KFold(n_splits=5, shuffle=True, random_state=42)\n", "    cv_scores_mse = cross_val_score(model, X, y, cv=cv, scoring='neg_mean_squared_error')\n", "    cv_rmse = np.sqrt(-cv_scores_mse.mean())\n", "    cv_scores_r2 = cross_val_score(model, X, y, cv=cv, scoring='r2')\n", "    cv_r2 = cv_scores_r2.mean()\n", "    feature_importance = {}\n", "    if hasattr(model, 'coef_'):\n", "        feature_importance = dict(zip(X.columns, np.abs(model.coef_)))\n", "    <PERSON><PERSON> has<PERSON>(model, 'feature_importances_'):\n", "        feature_importance = dict(zip(X.columns, model.feature_importances_))\n", "    top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:10]\n", "    return {'mse': mse, 'rmse': rmse, 'r2': r2, 'mape': mape, 'cv_rmse': cv_rmse, 'cv_r2': cv_r2, 'top_features': top_features}\n", "\n", "def evaluate_poverty_dimension_model(model, X, y):\n", "    y_pred = model.predict(X)\n", "    y_pred_proba = model.predict_proba(X)[:, 1] if hasattr(model, 'predict_proba') else None\n", "    accuracy = accuracy_score(y, y_pred)\n", "    precision = precision_score(y, y_pred, zero_division=0)\n", "    recall = recall_score(y, y_pred, zero_division=0)\n", "    f1 = f1_score(y, y_pred, zero_division=0)\n", "    roc_auc = None\n", "    if y_pred_proba is not None:\n", "        try:\n", "            roc_auc = roc_auc_score(y, y_pred_proba)\n", "        except:\n", "            roc_auc = None\n", "    cv = KFold(n_splits=5, shuffle=True, random_state=42)\n", "    cv_scores = cross_val_score(model, X, y, cv=cv, scoring='accuracy')\n", "    cv_accuracy = cv_scores.mean()\n", "    feature_importance = {}\n", "    if hasattr(model, 'feature_importances_'):\n", "        feature_importance = dict(zip(X.columns, model.feature_importances_))\n", "    top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:10]\n", "    try:\n", "        class_report = classification_report(y, y_pred, output_dict=True)\n", "    except:\n", "        class_report = {}\n", "    return {'accuracy': accuracy, 'precision': precision, 'recall': recall, 'f1': f1, 'roc_auc': roc_auc, 'cv_accuracy': cv_accuracy, 'top_features': top_features, 'classification_report': class_report}\n", "\n", "def train_all_models(X, y_dict, test_size=0.2):\n", "    from sklearn.model_selection import train_test_split\n", "    models_dict = {}\n", "    metrics_dict = {}\n", "    for target_name, target_values in y_dict.items():\n", "        X_train, X_test, y_train, y_test = train_test_split(X, target_values, test_size=test_size, random_state=42)\n", "        if target_name == 'income' or target_name == 'hhincomepm':\n", "            model = create_income_model(X_train, y_train)\n", "            models_dict[target_name] = model\n", "            metrics = evaluate_income_model(model, X_test, y_test)\n", "            metrics_dict[target_name] = metrics\n", "        elif target_name.endswith('_poor'):\n", "            model = create_poverty_dimension_model(X_train, y_train)\n", "            models_dict[target_name] = model\n", "            metrics = evaluate_poverty_dimension_model(model, X_test, y_test)\n", "            metrics_dict[target_name] = metrics\n", "    return models_dict, metrics_dict\n", "\n", "def get_recommended_interventions(dimension_results):\n", "    recommendations = {}\n", "    if dimension_results.get('income_poor', False):\n", "        recommendations['income_poor'] = {\n", "            'title': 'Income Support Interventions',\n", "            'recommendations': [\n", "                'Income supplement programs for low-income households',\n", "                'Skills development and vocational training',\n", "                'Employment opportunity creation',\n", "                'Micro-financing for small business development',\n", "                'Debt management assistance'\n", "            ]\n", "        }\n", "    if dimension_results.get('education_poor', False):\n", "        recommendations['education_poor'] = {\n", "            'title': 'Education Support Interventions',\n", "            'recommendations': [\n", "                'School enrollment subsidies for children',\n", "                'Adult education programs',\n", "                'Educational materials support',\n", "                'School transportation assistance',\n", "                'Educational debt relief programs'\n", "            ]\n", "        }\n", "    if dimension_results.get('health_poor', False):\n", "        recommendations['health_poor'] = {\n", "            'title': 'Health Support Interventions',\n", "            'recommendations': [\n", "                'Universal health coverage enrollment',\n", "                'Mobile health clinics for remote areas',\n", "                'Chronic disease management programs',\n", "                'Disability support services',\n", "                'Health education and preventive care'\n", "            ]\n", "        }\n", "    if dimension_results.get('living_standards_poor', False):\n", "        recommendations['living_standards_poor'] = {\n", "            'title': 'Living Standards Support',\n", "            'recommendations': [\n", "                'Housing improvement assistance',\n", "                'Water and sanitation facilities improvement',\n", "                'Energy access programs',\n", "                'Basic household items provision',\n", "                'Transportation infrastructure improvement'\n", "            ]\n", "        }\n", "    if dimension_results.get('multidimensionally_poor', False):\n", "        recommendations['multidimensionally_poor'] = {\n", "            'title': 'Comprehensive Support Package',\n", "            'recommendations': [\n", "                'Integrated case management across all dimensions',\n", "                'Regular monitoring and follow-up',\n", "                'Community-based support networks',\n", "                'Prioritized access to multiple support programs',\n", "                'Family development planning and counseling'\n", "            ]\n", "        }\n", "    return recommendations"]}, {"cell_type": "markdown", "id": "d997d559", "metadata": {}, "source": ["---\n", "# utils.py: Data Preparation and Visualization Utilities\n", "This section contains all data generation, feature engineering, and plotting utilities used in the app."]}, {"cell_type": "code", "execution_count": null, "id": "0e7c75f9", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "def generate_sample_data(n_samples=1000, random_seed=42):\n", "    np.random.seed(random_seed)\n", "    data = pd.DataFrame({\n", "        'hhsize': np.random.randint(1, 15, size=n_samples),\n", "        'house_size_sqft': np.random.randint(100, 10000, size=n_samples),\n", "        'district': np.random.choice(['Colombo', 'Gampaha', 'Kandy'], size=n_samples),\n", "        'sector': np.random.choice(['Urban', 'Rural', 'Estate'], size=n_samples),\n", "        'education_years': np.random.randint(0, 20, size=n_samples),\n", "        'health_index': np.random.uniform(0, 1, size=n_samples),\n", "        'living_standards_index': np.random.uniform(0, 1, size=n_samples),\n", "        'income': np.random.randint(10000, 200000, size=n_samples)\n", "    })\n", "    return data\n", "\n", "def prepare_features(df):\n", "    df['sqft_per_person'] = df['house_size_sqft'] / df['hhsize']\n", "    df['education_poor'] = (df['education_years'] < 10).astype(int)\n", "    df['health_poor'] = (df['health_index'] < 0.5).astype(int)\n", "    df['living_standards_poor'] = (df['living_standards_index'] < 0.5).astype(int)\n", "    return df\n", "\n", "def load_and_prepare_data(uploaded_file, target_columns):\n", "    if uploaded_file.name.endswith('.csv'):\n", "        data = pd.read_csv(uploaded_file)\n", "    else:\n", "        data = pd.read_excel(uploaded_file)\n", "    data = prepare_features(data)\n", "    X = data.drop(columns=target_columns)\n", "    y_dict = {col: data[col] for col in target_columns}\n", "    return X, y_dict\n", "\n", "def engineer_hies_features(df):\n", "    df['sqft_per_person'] = df['house_size_sqft'] / df['hhsize']\n", "    df['education_poor'] = (df['education_years'] < 10).astype(int)\n", "    df['health_poor'] = (df['health_index'] < 0.5).astype(int)\n", "    df['living_standards_poor'] = (df['living_standards_index'] < 0.5).astype(int)\n", "    return df\n", "\n", "def plot_income_prediction(predicted_income, threshold):\n", "    fig, ax = plt.subplots()\n", "    ax.bar(['Predicted Income', 'Threshold'], [predicted_income, threshold], color=['blue', 'red'])\n", "    ax.set_ylabel('Income (LKR)')\n", "    ax.set_title('Income Prediction vs Threshold')\n", "    return fig\n", "\n", "def plot_poverty_dimensions(dimension_results):\n", "    fig, ax = plt.subplots()\n", "    dimensions = list(dimension_results.keys())\n", "    values = list(dimension_results.values())\n", "    ax.bar(dimensions, values, color=['green' if v == 0 else 'red' for v in values])\n", "    ax.set_ylabel('Poverty Status')\n", "    ax.set_title('Poverty Dimensions')\n", "    return fig\n", "\n", "def plot_feature_importance(model, feature_names, title=\"Feature Importance\"):\n", "    importance = model.feature_importances_\n", "    sorted_indices = np.argsort(importance)[::-1]\n", "    sorted_features = [feature_names[i] for i in sorted_indices]\n", "    sorted_importance = importance[sorted_indices]\n", "    fig, ax = plt.subplots()\n", "    ax.bar(sorted_features[:10], sorted_importance[:10])\n", "    ax.set_ylabel('Importance')\n", "    ax.set_title(title)\n", "    return fig"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}