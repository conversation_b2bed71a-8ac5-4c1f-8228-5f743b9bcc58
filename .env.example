# Environment Configuration for Poverty Classification System
# Copy this file to .env and update the values for your environment

# Application Settings
APP_NAME="Poverty Classification System"
APP_VERSION="1.0.0"
ENVIRONMENT=development  # development, testing, production
DEBUG=true

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_PREFIX=/api/v1

# Frontend Configuration
FRONTEND_HOST=0.0.0.0
FRONTEND_PORT=8501

# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/poverty_classifier
DB_HOST=localhost
DB_PORT=5432
DB_NAME=poverty_classifier
DB_USER=postgres
DB_PASSWORD=postgres
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Security Configuration
SECRET_KEY=your-secret-key-change-in-production-must-be-very-long-and-random
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
ALGORITHM=HS256

# CORS Configuration
ALLOWED_HOSTS=*
CORS_ORIGINS=["http://localhost:3000","http://localhost:8501","http://127.0.0.1:3000","http://127.0.0.1:8501"]

# File Upload Configuration
MAX_UPLOAD_SIZE=52428800  # 50MB in bytes
ALLOWED_EXTENSIONS=[".csv",".xlsx",".xls"]
UPLOAD_DIR=uploads

# ML Model Configuration
MODEL_DIR=models
DEFAULT_POVERTY_THRESHOLD=50000
MODEL_RETRAIN_INTERVAL_HOURS=24

# Logging Configuration
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE=logs/app.log

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090

# Email Configuration (for notifications)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_TLS=true

# Celery Configuration (for background tasks)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Production-specific settings (uncomment for production)
# ENVIRONMENT=production
# DEBUG=false
# SECRET_KEY=generate-a-very-secure-random-key-for-production
# ALLOWED_HOSTS=["yourdomain.com","www.yourdomain.com"]
# DATABASE_URL=********************************************/poverty_classifier
# LOG_LEVEL=WARNING
