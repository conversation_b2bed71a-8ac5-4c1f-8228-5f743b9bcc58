import pytest
import numpy as np
import pandas as pd
from models import create_income_model, evaluate_income_model

@pytest.fixture
def sample_data():
    X = pd.DataFrame(np.random.rand(100, 5), columns=[f'feature_{i}' for i in range(5)])
    y = pd.Series(np.random.rand(100))
    return X, y

def test_create_income_model(sample_data):
    X, y = sample_data
    model = create_income_model(X, y)
    assert model is not None
    assert hasattr(model, 'predict')

def test_evaluate_income_model(sample_data):
    X, y = sample_data
    model = create_income_model(X, y)
    evaluation = evaluate_income_model(model, X, y)
    assert isinstance(evaluation, dict)
    assert 'r2' in evaluation
    assert 'rmse' in evaluation
    assert 'mape' in evaluation
    assert 'cv_r2' in evaluation
    assert 'top_features' in evaluation
    assert isinstance(evaluation['top_features'], list)

pytest test_app_ui.py