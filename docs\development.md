# Development Guide - Poverty Classification System

## Overview

This guide provides comprehensive information for developers working on the Poverty Classification System. The system is built with modern technologies and follows best practices for maintainability, scalability, and security.

## Architecture

### Technology Stack

**Backend:**
- **FastAPI** - Modern, fast web framework for building APIs
- **SQLAlchemy** - SQL toolkit and ORM
- **PostgreSQL** - Primary database
- **Redis** - Caching and session storage
- **Celery** - Background task processing

**Frontend:**
- **Streamlit** - Interactive web application framework
- **Plotly** - Interactive data visualization
- **Pandas** - Data manipulation and analysis

**Machine Learning:**
- **scikit-learn** - Machine learning algorithms
- **NumPy** - Numerical computing
- **Pandas** - Data processing

**Infrastructure:**
- **Docker** - Containerization
- **Docker Compose** - Multi-container orchestration
- **Nginx** - Reverse proxy (production)

### Project Structure

```
poverty-classifier/
├── src/                        # Core application code
│   ├── api/                   # FastAPI backend
│   │   ├── main.py           # FastAPI application
│   │   ├── middleware.py     # Custom middleware
│   │   ├── routers/          # API route handlers
│   │   └── schemas/          # Pydantic models
│   ├── core/                 # Core utilities
│   │   └── logging.py        # Logging configuration
│   ├── database/             # Database layer
│   │   ├── connection.py     # Database connection
│   │   └── models.py         # SQLAlchemy models
│   └── services/             # Business logic
│       └── prediction_service.py
├── frontend/                  # Streamlit frontend
│   └── app.py                # Main frontend application
├── config/                   # Configuration management
│   └── settings.py           # Application settings
├── scripts/                  # Deployment and utility scripts
│   ├── init_db.py           # Database initialization
│   ├── deploy.py            # Deployment script
│   └── start_dev.py         # Development server
├── tests/                    # Test suite
├── docs/                     # Documentation
├── docker/                   # Docker configurations
├── models.py                 # Legacy ML models (to be migrated)
├── utils/                    # Legacy utilities (to be migrated)
├── requirements.txt          # Python dependencies
├── Dockerfile               # Docker image definition
├── docker-compose.yml       # Multi-container setup
└── README.md                # Project overview
```

## Development Setup

### Prerequisites

- Python 3.9+
- PostgreSQL 12+
- Redis 6+
- Docker & Docker Compose (optional)

### Quick Start

1. **Clone and Setup:**
   ```bash
   git clone <repository-url>
   cd poverty-classifier
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install Dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Environment Configuration:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Database Setup:**
   ```bash
   python scripts/init_db.py
   ```

5. **Start Development Servers:**
   ```bash
   python scripts/start_dev.py
   ```

### Manual Server Start

If you prefer to start servers manually:

```bash
# Terminal 1 - API Server
uvicorn src.api.main:app --reload --port 8000

# Terminal 2 - Frontend Server
streamlit run frontend/app.py --server.port 8501
```

## Development Workflow

### Code Organization

**API Development:**
- Add new endpoints in `src/api/routers/`
- Define request/response schemas in `src/api/schemas/`
- Implement business logic in `src/services/`
- Add database models in `src/database/models.py`

**Frontend Development:**
- Main application logic in `frontend/app.py`
- Create reusable components as functions
- Use Streamlit's session state for state management

### Database Migrations

When modifying database models:

1. **Create Migration:**
   ```bash
   alembic revision --autogenerate -m "Description of changes"
   ```

2. **Apply Migration:**
   ```bash
   alembic upgrade head
   ```

3. **Rollback Migration:**
   ```bash
   alembic downgrade -1
   ```

### Testing

**Run Tests:**
```bash
# All tests
pytest

# Specific test file
pytest tests/test_api.py

# With coverage
pytest --cov=src tests/
```

**Test Structure:**
- `tests/unit/` - Unit tests
- `tests/integration/` - Integration tests
- `tests/api/` - API endpoint tests

### Code Quality

**Linting and Formatting:**
```bash
# Format code
black src/ frontend/ tests/

# Sort imports
isort src/ frontend/ tests/

# Lint code
flake8 src/ frontend/ tests/

# Type checking
mypy src/
```

**Pre-commit Hooks:**
```bash
# Install pre-commit
pip install pre-commit
pre-commit install

# Run manually
pre-commit run --all-files
```

## API Development

### Adding New Endpoints

1. **Create Router:**
   ```python
   # src/api/routers/new_feature.py
   from fastapi import APIRouter, Depends
   from src.api.routers.auth import get_current_active_user
   
   router = APIRouter()
   
   @router.get("/")
   async def get_items(current_user = Depends(get_current_active_user)):
       return {"items": []}
   ```

2. **Add Schemas:**
   ```python
   # src/api/schemas/new_feature.py
   from pydantic import BaseModel
   
   class ItemCreate(BaseModel):
       name: str
       description: str
   ```

3. **Register Router:**
   ```python
   # src/api/main.py
   from .routers import new_feature
   
   app.include_router(
       new_feature.router,
       prefix="/api/v1/items",
       tags=["Items"]
   )
   ```

### Authentication & Authorization

**Require Authentication:**
```python
from src.api.routers.auth import get_current_active_user

@router.get("/protected")
async def protected_endpoint(current_user = Depends(get_current_active_user)):
    return {"user": current_user.username}
```

**Require Specific Role:**
```python
from src.api.routers.auth import require_role

@router.get("/admin-only")
async def admin_endpoint(current_user = Depends(require_role("admin"))):
    return {"message": "Admin access granted"}
```

### Database Operations

**Create Model:**
```python
# src/database/models.py
class NewModel(Base):
    __tablename__ = "new_models"
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
```

**Database Operations:**
```python
from sqlalchemy.orm import Session
from src.database.connection import get_db

@router.post("/")
async def create_item(item_data: ItemCreate, db: Session = Depends(get_db)):
    db_item = NewModel(**item_data.dict())
    db.add(db_item)
    db.commit()
    db.refresh(db_item)
    return db_item
```

## Frontend Development

### Streamlit Best Practices

**State Management:**
```python
# Initialize session state
if 'key' not in st.session_state:
    st.session_state.key = default_value

# Use session state
st.session_state.key = new_value
```

**API Integration:**
```python
def make_api_request(endpoint, method="GET", data=None):
    headers = {"Authorization": f"Bearer {st.session_state.access_token}"}
    response = requests.request(method, f"{API_BASE_URL}{endpoint}", 
                               json=data, headers=headers)
    return response.json()
```

**Error Handling:**
```python
try:
    result = make_api_request("/api/v1/predict", "POST", data)
    st.success("Prediction successful!")
except requests.exceptions.RequestException as e:
    st.error(f"API Error: {str(e)}")
```

## Machine Learning

### Model Development

**Adding New Models:**
1. Implement in `src/services/prediction_service.py`
2. Add model training logic
3. Update prediction endpoints
4. Add model versioning

**Model Training:**
```python
from src.services.prediction_service import PredictionService

service = PredictionService()
service.retrain_models(training_data)
```

### Model Deployment

**Model Versioning:**
- Models are saved with version numbers
- Track performance metrics
- Support A/B testing

## Deployment

### Docker Development

**Build and Run:**
```bash
# Build image
docker build -t poverty-classifier .

# Run with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f api
```

### Production Deployment

**Environment Setup:**
```bash
# Set production environment
export ENVIRONMENT=production
export SECRET_KEY=your-production-secret-key
export DATABASE_URL=********************************/db
```

**Deploy:**
```bash
python scripts/deploy.py --mode docker
```

## Monitoring & Debugging

### Logging

**Application Logs:**
```python
from src.core.logging import get_logger

logger = get_logger(__name__)
logger.info("Operation completed")
logger.error("Error occurred", exc_info=True)
```

**Log Locations:**
- Development: Console output
- Production: `logs/app.log`

### Health Monitoring

**Health Endpoints:**
- `/health` - Basic health check
- `/health/ready` - Readiness check
- `/health/live` - Liveness check

### Performance Monitoring

**Metrics Collection:**
- Request/response times
- Error rates
- Database query performance
- Memory and CPU usage

## Security

### Best Practices

1. **Authentication:**
   - Use JWT tokens
   - Implement token refresh
   - Secure password hashing

2. **Authorization:**
   - Role-based access control
   - Endpoint-level permissions
   - Resource-level access

3. **Data Protection:**
   - Input validation
   - SQL injection prevention
   - XSS protection

4. **Infrastructure:**
   - HTTPS in production
   - Security headers
   - Rate limiting

## Contributing

### Code Standards

1. **Python:**
   - Follow PEP 8
   - Use type hints
   - Write docstrings
   - Maximum line length: 88 characters

2. **Git Workflow:**
   - Feature branches
   - Descriptive commit messages
   - Pull request reviews

3. **Documentation:**
   - Update relevant documentation
   - Add inline comments for complex logic
   - Update API documentation

### Pull Request Process

1. Create feature branch
2. Implement changes
3. Add/update tests
4. Update documentation
5. Submit pull request
6. Address review feedback
7. Merge after approval

## Troubleshooting

### Common Issues

**Database Connection:**
```bash
# Check PostgreSQL status
pg_isready -h localhost -p 5432

# Reset database
python scripts/init_db.py
```

**API Server Issues:**
```bash
# Check logs
tail -f logs/app.log

# Restart server
pkill -f uvicorn
python scripts/start_dev.py
```

**Frontend Issues:**
```bash
# Clear Streamlit cache
streamlit cache clear

# Restart frontend
pkill -f streamlit
streamlit run frontend/app.py
```

## Support

For development support:
- **Documentation:** Check this guide and API docs
- **Issues:** Create GitHub issues for bugs
- **Discussions:** Use GitHub discussions for questions
