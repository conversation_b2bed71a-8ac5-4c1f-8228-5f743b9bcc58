"""
Health check endpoints for monitoring and load balancers
"""

from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, status
from sqlalchemy.orm import Session
import time
import psutil
import os

from src.database.connection import get_db, check_db_connection
from src.core.logging import get_logger
from config.settings import settings

logger = get_logger(__name__)
router = APIRouter()


@router.get("/")
async def health_check():
    """Basic health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": settings.APP_VERSION,
        "environment": settings.ENVIRONMENT
    }


@router.get("/ready")
async def readiness_check(db: Session = Depends(get_db)):
    """Readiness check - verifies all dependencies are available"""
    checks = {
        "database": False,
        "disk_space": False,
        "memory": False
    }
    
    overall_status = "healthy"
    
    try:
        # Database check
        checks["database"] = check_db_connection()
        
        # Disk space check (at least 1GB free)
        disk_usage = psutil.disk_usage('/')
        free_gb = disk_usage.free / (1024**3)
        checks["disk_space"] = free_gb > 1.0
        
        # Memory check (at least 100MB available)
        memory = psutil.virtual_memory()
        available_mb = memory.available / (1024**2)
        checks["memory"] = available_mb > 100
        
        # Overall status
        if not all(checks.values()):
            overall_status = "unhealthy"
            
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        overall_status = "unhealthy"
    
    status_code = status.HTTP_200_OK if overall_status == "healthy" else status.HTTP_503_SERVICE_UNAVAILABLE
    
    return {
        "status": overall_status,
        "checks": checks,
        "timestamp": time.time()
    }


@router.get("/live")
async def liveness_check():
    """Liveness check - basic application responsiveness"""
    return {
        "status": "alive",
        "timestamp": time.time(),
        "uptime": time.time() - psutil.boot_time()
    }


@router.get("/metrics")
async def metrics():
    """Basic system metrics for monitoring"""
    try:
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Memory usage
        memory = psutil.virtual_memory()
        
        # Disk usage
        disk = psutil.disk_usage('/')
        
        # Process info
        process = psutil.Process(os.getpid())
        process_memory = process.memory_info()
        
        return {
            "system": {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_available_mb": memory.available / (1024**2),
                "disk_percent": (disk.used / disk.total) * 100,
                "disk_free_gb": disk.free / (1024**3)
            },
            "process": {
                "memory_rss_mb": process_memory.rss / (1024**2),
                "memory_vms_mb": process_memory.vms / (1024**2),
                "cpu_percent": process.cpu_percent(),
                "num_threads": process.num_threads(),
                "create_time": process.create_time()
            },
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error(f"Metrics collection failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to collect metrics"
        )
