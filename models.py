import numpy as np
import pandas as pd
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.model_selection import cross_val_score, KFold
from sklearn.metrics import mean_squared_error, r2_score, accuracy_score, precision_score, recall_score, f1_score, classification_report, roc_auc_score

def create_income_model(X, y):
    """
    Creates and trains a model for income prediction that ensures positive predictions.
    
    Parameters:
    -----------
    X : pandas.DataFrame
        Features for training the model
    y : pandas.Series
        Target income values
    
    Returns:
    --------
    object
        Trained model with predict method
    """
    # For income prediction, use a Random Forest Regressor instead of Linear Regression
    # This typically handles the data better and avoids extreme negative predictions
    model = RandomForestRegressor(
        n_estimators=100,
        max_depth=None,
        min_samples_split=2,
        min_samples_leaf=1,
        random_state=42
    )
    
    # Ensure training data has no negative values
    y_train = np.maximum(y, 0)
    
    # Train the model
    model.fit(X, y_train)
    
    # Create a wrapper to ensure predictions are always positive
    class PositiveIncomeModel:
        def __init__(self, base_model):
            self.base_model = base_model
            
        def predict(self, X):
            # Get base predictions
            preds = self.base_model.predict(X)
            # Apply minimum income threshold (10,000 LKR is a reasonable minimum for Sri Lanka)
            return np.maximum(preds, 10000)
            
        def __getattr__(self, attr):
            # Pass through any other attribute to the base model
            return getattr(self.base_model, attr)
    
    return PositiveIncomeModel(model)

def create_poverty_dimension_model(X, y):
    """
    Creates and trains a random forest classifier for poverty dimension classification.
    
    Parameters:
    -----------
    X : pandas.DataFrame
        Features for training the model
    y : pandas.Series
        Binary target indicating poverty in a specific dimension
    
    Returns:
    --------
    sklearn.ensemble.RandomForestClassifier
        Trained random forest classifier
    """
    # Create a random forest classifier with optimized hyperparameters
    model = RandomForestClassifier(
        n_estimators=100,
        max_depth=10,
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=42,
        class_weight='balanced'  # Important for imbalanced classes
    )
    
    # Train the model
    model.fit(X, y)
    
    return model

def evaluate_income_model(model, X, y):
    """
    Evaluates the income prediction model using cross-validation.
    
    Parameters:
    -----------
    model : sklearn.linear_model.LinearRegression
        Trained linear regression model
    X : pandas.DataFrame
        Features for evaluation
    y : pandas.Series
        Target income values
    
    Returns:
    --------
    dict
        Dictionary containing evaluation metrics
    """
    # Predict income
    y_pred = model.predict(X)
    
    # Calculate metrics
    mse = mean_squared_error(y, y_pred)
    rmse = np.sqrt(mse)
    r2 = r2_score(y, y_pred)
    
    # Calculate Mean Absolute Percentage Error (MAPE)
    mape = np.mean(np.abs((y - y_pred) / np.maximum(y, 1))) * 100
    
    # Cross-validation
    cv = KFold(n_splits=5, shuffle=True, random_state=42)
    cv_scores_mse = cross_val_score(model, X, y, cv=cv, scoring='neg_mean_squared_error')
    cv_rmse = np.sqrt(-cv_scores_mse.mean())
    
    cv_scores_r2 = cross_val_score(model, X, y, cv=cv, scoring='r2')
    cv_r2 = cv_scores_r2.mean()
    
    # Calculate feature importances if the model supports it
    feature_importance = {}
    if hasattr(model, 'coef_'):
        # Linear regression coefficients
        feature_importance = dict(zip(X.columns, np.abs(model.coef_)))
    elif hasattr(model, 'feature_importances_'):
        # Tree-based model importances
        feature_importance = dict(zip(X.columns, model.feature_importances_))
    
    # Sort feature importance
    top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:10]
    
    return {
        'mse': mse,
        'rmse': rmse,
        'r2': r2,
        'mape': mape,
        'cv_rmse': cv_rmse,
        'cv_r2': cv_r2,
        'top_features': top_features
    }

def evaluate_poverty_dimension_model(model, X, y):
    """
    Evaluates the poverty dimension classification model.
    
    Parameters:
    -----------
    model : sklearn.ensemble.RandomForestClassifier
        Trained random forest classifier
    X : pandas.DataFrame
        Features for evaluation
    y : pandas.Series
        Binary target indicating poverty in a specific dimension
    
    Returns:
    --------
    dict
        Dictionary containing evaluation metrics
    """
    # Predict classification
    y_pred = model.predict(X)
    y_pred_proba = model.predict_proba(X)[:, 1] if hasattr(model, 'predict_proba') else None
    
    # Calculate metrics
    accuracy = accuracy_score(y, y_pred)
    precision = precision_score(y, y_pred, zero_division=0)
    recall = recall_score(y, y_pred, zero_division=0)
    f1 = f1_score(y, y_pred, zero_division=0)
    
    # Calculate ROC AUC if probabilities are available
    roc_auc = None
    if y_pred_proba is not None:
        try:
            roc_auc = roc_auc_score(y, y_pred_proba)
        except:
            # In case of single class prediction
            roc_auc = None
    
    # Cross-validation
    cv = KFold(n_splits=5, shuffle=True, random_state=42)
    cv_scores = cross_val_score(model, X, y, cv=cv, scoring='accuracy')
    cv_accuracy = cv_scores.mean()
    
    # Calculate feature importances
    feature_importance = {}
    if hasattr(model, 'feature_importances_'):
        feature_importance = dict(zip(X.columns, model.feature_importances_))
    
    # Sort feature importance
    top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:10]
    
    # Get detailed classification report
    try:
        class_report = classification_report(y, y_pred, output_dict=True)
    except:
        class_report = {}
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'roc_auc': roc_auc,
        'cv_accuracy': cv_accuracy,
        'top_features': top_features,
        'classification_report': class_report
    }

def train_all_models(X, y_dict, test_size=0.2):
    """
    Trains all models for income prediction and poverty dimensions.
    
    Parameters:
    -----------
    X : pandas.DataFrame
        Features for training
    y_dict : dict
        Dictionary mapping target names to target values
    test_size : float
        Proportion of data to use for testing
    
    Returns:
    --------
    tuple
        (models_dict, metrics_dict) where models_dict maps target names to trained models
        and metrics_dict maps target names to evaluation metrics
    """
    from sklearn.model_selection import train_test_split
    models_dict = {}
    metrics_dict = {}
    for target_name, target_values in y_dict.items():
        X_train, X_test, y_train, y_test = train_test_split(
            X, target_values, test_size=test_size, random_state=42
        )
        if target_name == 'income' or target_name == 'hhincomepm':
            model = create_income_model(X_train, y_train)
            models_dict[target_name] = model
            metrics = evaluate_income_model(model, X_test, y_test)
            metrics_dict[target_name] = metrics
        elif target_name.endswith('_poor'):
            model = create_poverty_dimension_model(X_train, y_train)
            models_dict[target_name] = model
            metrics = evaluate_poverty_dimension_model(model, X_test, y_test)
            metrics_dict[target_name] = metrics
    return models_dict, metrics_dict

def get_recommended_interventions(dimension_results):
    """
    Provides appropriate intervention recommendations based on poverty dimension results.
    
    Parameters:
    -----------
    dimension_results : dict
        Dictionary containing results for each poverty dimension
        
    Returns:
    --------
    dict
        Dictionary mapping poverty dimensions to intervention recommendations
    """
    recommendations = {}
    
    # Income poverty interventions
    if dimension_results.get('income_poor', False):
        recommendations['income_poor'] = {
            'title': 'Income Support Interventions',
            'recommendations': [
                'Income supplement programs for low-income households',
                'Skills development and vocational training',
                'Employment opportunity creation',
                'Micro-financing for small business development',
                'Debt management assistance'
            ]
        }
    
    # Education poverty interventions
    if dimension_results.get('education_poor', False):
        recommendations['education_poor'] = {
            'title': 'Education Support Interventions',
            'recommendations': [
                'School enrollment subsidies for children',
                'Adult education programs',
                'Educational materials support',
                'School transportation assistance',
                'Educational debt relief programs'
            ]
        }
    
    # Health poverty interventions
    if dimension_results.get('health_poor', False):
        recommendations['health_poor'] = {
            'title': 'Health Support Interventions',
            'recommendations': [
                'Universal health coverage enrollment',
                'Mobile health clinics for remote areas',
                'Chronic disease management programs',
                'Disability support services',
                'Health education and preventive care'
            ]
        }
    
    # Living standards poverty interventions
    if dimension_results.get('living_standards_poor', False):
        recommendations['living_standards_poor'] = {
            'title': 'Living Standards Support',
            'recommendations': [
                'Housing improvement assistance',
                'Water and sanitation facilities improvement',
                'Energy access programs',
                'Basic household items provision',
                'Transportation infrastructure improvement'
            ]
        }
    
    # Multidimensional poverty interventions
    if dimension_results.get('multidimensionally_poor', False):
        recommendations['multidimensionally_poor'] = {
            'title': 'Comprehensive Support Package',
            'recommendations': [
                'Integrated case management across all dimensions',
                'Regular monitoring and follow-up',
                'Community-based support networks',
                'Prioritized access to multiple support programs',
                'Family development planning and counseling'
            ]
        }
    
    return recommendations