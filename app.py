# Imports
import os
import base64
import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from models import (
    create_income_model, 
    create_poverty_dimension_model, 
    evaluate_income_model, 
    evaluate_poverty_dimension_model, 
    train_all_models, 
    get_recommended_interventions
)
from utils.utils import (
    generate_sample_data, 
    prepare_features, 
    load_and_prepare_data, 
    plot_income_prediction, 
    plot_poverty_dimensions, 
    plot_feature_importance, 
    engineer_hies_features
)

# Set page configuration
st.set_page_config(
    page_title="Poverty Classification System",
    page_icon="📊",
    layout="wide"
)

# Apply custom CSS styling
def load_css(css_file):
    with open(css_file, 'r') as f:
        css = f.read()
    st.markdown(f'<style>{css}</style>', unsafe_allow_html=True)

# Load custom CSS
if os.path.exists('style.css'):
    load_css('style.css')

# Custom CSS for dark blue/purple gradient background and white text
st.markdown('''
    <style>
    body, .stApp {
        background: linear-gradient(120deg, #0f144a 0%, #2d0b3a 100%) !important;
        color: #fff !important;
    }
    .stApp, .stMarkdown, .stTextInput, .stNumberInput, .stSelectbox, .stSlider, .stButton, .stDataFrame, .stMetric, .stCheckbox, .stExpander, .stTabs, .stHeader, .stSubheader, .stTable, .stAlert {
        color: #fff !important;
    }
    .css-1v3fvcr, .css-1c7y2kd, .css-1b0udgb, .css-1lcbmhc, .css-1p05t8e, .css-1vzeuhh {
        color: #fff !important;
    }
    .stButton>button {
        background-color: #2d0b3a !important;
        color: #fff !important;
        border: 1px solid #fff !important;
    }
    .stDataFrame, .stTable {
        background: rgba(15,20,74,0.85) !important;
        color: #fff !important;
    }
    .stTabs [data-baseweb="tab"] {
        background: #1a174d !important;
        color: #fff !important;
    }
    .stExpanderHeader {
        color: #fff !important;
    }
    </style>
''', unsafe_allow_html=True)

# Define constants
DEFAULT_POVERTY_THRESHOLD = 50000  # Default threshold in LKR
TARGET_COLUMNS = ['hhincomepm', 'income_poor', 'education_poor', 'health_poor', 'living_standards_poor', 'multidimensionally_poor']

# Add default thresholds for poverty dimensions
DEFAULT_THRESHOLDS = {
    'health': 0.5,
    'education': 0.5,
    'living_standards': 0.5,
    'multidimensional': 0.5
}

for key, value in DEFAULT_THRESHOLDS.items():
    session_key = f'{key}_poverty_threshold'
    if session_key not in st.session_state:
        st.session_state[session_key] = value

# Initialize session state variables if they don't exist
if 'models_trained' not in st.session_state:
    st.session_state.models_trained = False
    
if 'using_custom_data' not in st.session_state:
    st.session_state.using_custom_data = False
    
if 'data_uploaded' not in st.session_state:
    st.session_state.data_uploaded = False
    
if 'threshold' not in st.session_state:
    st.session_state.threshold = DEFAULT_POVERTY_THRESHOLD
    
if 'models' not in st.session_state:
    st.session_state.models = {}
    
if 'metrics' not in st.session_state:
    st.session_state.metrics = {}

if 'poverty_dimension_models' not in st.session_state:
    st.session_state.poverty_dimension_models = {}

# Main app title
st.title("Household Poverty Classification System")
st.markdown("""
This application predicts household income using Linear Regression and classifies 
poverty dimensions using Random Forest classifiers. Upload your household dataset 
or input household information to receive predictions on income level and poverty classification.
""")

# Adjustable income poverty threshold input
st.subheader("Current Income Poverty Threshold")
st.session_state.threshold = st.number_input(
    "Set Income Poverty Threshold (LKR)",
    min_value=10000,
    max_value=200000,
    value=st.session_state.threshold,
    step=5000,
    format="%d",
    key="main_income_poverty_threshold"
)
st.markdown(f"**LKR {st.session_state.threshold:,}**")

# Data uploading and model training section
with st.expander("📥 Upload Dataset & Train Models", expanded=not st.session_state.models_trained):
    st.markdown("""    ### Upload Your Dataset
    
    Upload a CSV or Excel file containing preprocessed household survey data.
    The file should include features like:
    - `hhsize`: Household size
    - `education_level_max`: Maximum education level in household
    - `education_years_mean`: Mean years of education in household
    - `school_attendance_ratio`: Ratio of school-age children attending school
    - `chronic_illness`, `health_insurance`, `disability`: Health indicators
    - `housing_quality_index`, `adequate_sanitation`, `good_drinking_water`: Housing conditions
    - `weighted_durable_index`: Household durables index
    - Various debt columns (housing_debt, education_debt, etc.)
    - Accessibility times (bus_halt_time, hospital_time, etc.)
    
    Target columns (optional):
    - `hhincomepm`: Monthly household income
    - `income_poor`, `education_poor`, `health_poor`, `living_standards_poor`, `multidimensionally_poor`: Poverty indicators
    
    If target columns are missing, they will be derived from available features.
    """)
    
    uploaded_file = st.file_uploader("Choose a CSV or Excel file", type=["csv", "xlsx", "xls"])
    
    if uploaded_file is not None:
        try:
            # Process the uploaded file
            with st.spinner("Processing your dataset..."):
                X, y_dict = load_and_prepare_data(uploaded_file, TARGET_COLUMNS)
                
                # Store the data in session state
                st.session_state.X = X
                st.session_state.y_dict = y_dict
                st.session_state.data_uploaded = True
                
                # Display dataset information
                # Check for data quality and required columns
                required_columns = ['hhsize', 'district_encoded', 'housing_quality_index']
                missing_cols = [col for col in required_columns if col not in X.columns]
                
                if missing_cols:
                    st.warning(f"⚠️ Some recommended columns are missing: {', '.join(missing_cols)}")
                    st.info("""
                    The application will attempt to generate reasonable values for missing columns, 
                    but for best results, consider adding these columns to your dataset.
                    
                    Key columns for accurate predictions:
                    - **hhsize**: Household size (number of members)
                    - **district_encoded/district**: Geographic location
                    - **housing_quality_index**: Quality of housing on scale 1-10
                    - **education_level_max**: Highest education level in household
                    - **num_employed**: Number of employed household members
                    """)
                
                if 'hhincomepm' not in y_dict:
                    st.warning("⚠️ Income data (hhincomepm) is missing. The application will attempt to predict income solely from other features.")
                
                # Show dataset summary
                st.success(f"Dataset loaded successfully with {X.shape[0]} rows and {X.shape[1]} columns.")
                
                # Display dataset information using tabs instead of nested expanders
                dataset_tab1, dataset_tab2, dataset_tab3 = st.tabs(["Dataset Features", "Target Variables", "Data Quality"])
                
                with dataset_tab1:
                    st.dataframe(X.head())
                    st.markdown(f"**Features:** {', '.join(X.columns)}")
                
                # Display target information if available
                if y_dict:
                    with dataset_tab2:
                        for target, data in y_dict.items():
                            if target == 'hhincomepm':
                                st.markdown(f"**{target}:** Mean = {data.mean():.2f}, Min = {data.min():.2f}, Max = {data.max():.2f}")
                            else:
                                pos_ratio = (data == 1).mean() * 100
                                st.markdown(f"**{target}:** Positive rate = {pos_ratio:.2f}%")
                
                # Display data quality information
                with dataset_tab3:
                    st.subheader("Data Quality Summary")
                    
                    # Check for missing values
                    missing_values = X.isnull().sum()
                    if missing_values.sum() > 0:
                        st.warning("⚠️ Some columns have missing values:")
                        st.write(missing_values[missing_values > 0])
                    else:
                        st.success("✅ No missing values detected in the dataset.")
                    
                    # Check for negative income values if income is present
                    if 'hhincomepm' in y_dict:
                        negative_income = (y_dict['hhincomepm'] < 0).sum()
                        if negative_income > 0:
                            st.error(f"❌ Dataset contains {negative_income} negative income values. This may cause prediction issues.")
                            st.info("Consider replacing negative income values with positive values or zeros.")
                        else:
                            st.success("✅ No negative income values detected.")
                    
                    # Check for extreme feature values
                    extreme_values = {}
                    for col in X.columns:
                        if X[col].dtype in [np.float64, np.int64]:
                            if X[col].max() > 1000000:  # Very large values
                                extreme_values[col] = X[col].max()
                    
                    if extreme_values:
                        st.warning("⚠️ Some columns have extremely large values which might affect model performance:")
                        for col, value in extreme_values.items():
                            st.write(f"- {col}: Max value = {value:,.2f}")
                    else:
                        st.success("✅ No extremely large feature values detected.")
                
        except Exception as e:
            st.error(f"Error loading dataset: {str(e)}")
    
    # Model training section
    if st.session_state.data_uploaded:
        st.markdown("### Train Models")
        
        train_col1, train_col2 = st.columns(2)
        
        with train_col1:
            # Train test split ratio input
            test_size = st.slider("Test Set Size (%)", min_value=10, max_value=50, value=20, step=5) / 100
            
            # Set poverty threshold from data if available
            if 'hhincomepm' in st.session_state.y_dict:
                income_mean = st.session_state.y_dict['hhincomepm'].mean()
                income_median = st.session_state.y_dict['hhincomepm'].median()
                # Suggest 60% of median income as poverty threshold (common approach)
                suggested_threshold = max(10000, int(income_median * 0.6))
                
                st.session_state.threshold = st.number_input(
                    "Poverty Threshold (LKR)",
                    min_value=int(income_mean * 0.2),
                    max_value=int(income_mean * 1.5),
                    value=suggested_threshold,
                    step=5000,
                    help="Threshold below which a household is considered income-poor. Default is 60% of median income."
                )
            else:
                st.session_state.threshold = st.number_input(
                    "Poverty Threshold (LKR)",
                    min_value=10000,
                    max_value=200000,
                    value=DEFAULT_POVERTY_THRESHOLD,
                    step=5000
                )
            
        with train_col2:
            if 'hhincomepm' not in st.session_state.y_dict:
                st.warning("Income target not found in dataset. The system will generate this target from other features if possible.")
            
            missing_targets = [target for target in TARGET_COLUMNS[1:] if target not in st.session_state.y_dict]
            if missing_targets:
                st.warning(f"Some poverty dimension targets not found: {', '.join(missing_targets)}. These will be derived from available features.")
        
        # Train model button
        if st.button("Train Models"):
            with st.spinner("Training models with your dataset..."):
                try:
                    X = st.session_state.X
                    y_dict = st.session_state.y_dict
                    total_models = len(y_dict)
                    progress_bar = st.progress(0, text="Training models...")
                    models = {}
                    metrics = {}
                    for idx, (target_name, target_values) in enumerate(y_dict.items()):
                        X_train, X_test, y_train, y_test = train_test_split(
                            X, target_values, test_size=test_size, random_state=42
                        )
                        if target_name == 'income' or target_name == 'hhincomepm':
                            model = create_income_model(X_train, y_train)
                            models[target_name] = model
                            metric = evaluate_income_model(model, X_test, y_test)
                            metrics[target_name] = metric
                        elif target_name.endswith('_poor'):
                            model = create_poverty_dimension_model(X_train, y_train)
                            models[target_name] = model
                            metric = evaluate_poverty_dimension_model(model, X_test, y_test)
                            metrics[target_name] = metric
                        progress = int((idx + 1) / total_models * 100)
                        progress_bar.progress(progress, text=f"Training models... {progress}% done")
                    st.session_state.models = models
                    st.session_state.metrics = metrics
                    st.session_state.models_trained = True
                    st.session_state.using_custom_data = True
                    st.success("All models trained successfully with your data!")
                    progress_bar.empty()
                except Exception as e:
                    st.error(f"Error training models: {str(e)}")
                    st.exception(e)
    else:        # If no custom data uploaded, inform about using default data
        if not st.session_state.models_trained:
            st.info("""
            No dataset uploaded yet. You can either:
            1. Upload your household dataset to train custom models, or
            2. Proceed with the default models trained on sample household data.
            """)
            
            if st.button("Use Default Models"):
                with st.spinner("Setting up default models..."):
                    # Generate sample data and train default models
                    sample_data = generate_sample_data(n_samples=2000)
                    X = prepare_features(sample_data)
                    
                    # Extract targets from sample data
                    y_dict = {}
                    for target in TARGET_COLUMNS:
                        if target in sample_data.columns:
                            y_dict[target] = sample_data[target]
                    
                    # Train all models
                    models, metrics = train_all_models(X, y_dict, test_size=0.2)
                    
                    # Store models and metrics in session state
                    st.session_state.models = models
                    st.session_state.metrics = metrics
                    
                    # Mark models as trained with default data
                    st.session_state.models_trained = True
                    st.session_state.using_custom_data = False
                    st.session_state.threshold = DEFAULT_POVERTY_THRESHOLD
                    
                    st.success("Default models trained successfully with sample data!")

                    # Show information about the sample data used for default models (no nested expander)
                    st.subheader("ℹ️ About the Sample Data Used in Default Models")
                    st.markdown("""
                    The default models are trained on **synthetic sample data** that represents typical household characteristics in Sri Lanka. This sample data includes:

                    - **Demographic:** Household size (`hhsize`), education level, mean years of education, school attendance ratio
                    - **Health:** Chronic illness, health insurance, disability, time to nearest hospital, medical debt
                    - **Living Standards:** Housing quality index, sanitation, drinking water, durable goods index
                    - **Economic:** Housing, education, medical, business, and other debts; accessibility times to services (bus halt, schools, market)
                    - **Income & Poverty:** Monthly household income (`hhincomepm`), and derived poverty indicators for income, education, health, living standards, and multidimensional poverty

                    The feature set and value ranges are designed to reflect real-world household conditions. This ensures that the default models provide realistic predictions and classifications even without a custom dataset.
                    """)
                    st.markdown("**Sample of the data used:**")
                    st.dataframe(sample_data.head())
                    st.markdown(f"**Columns:** {', '.join(sample_data.columns)}")

# Sidebar for model parameters and information
with st.sidebar:
    st.header("About the System")
    if st.session_state.using_custom_data:
        st.markdown("""
        This system is currently using **your uploaded dataset** to classify households based on:
        
        1. Income prediction using Linear Regression
        2. Income poverty classification using a threshold
        3. Detailed dimension analysis using Random Forest classifiers for:
           - Education poverty
           - Health poverty
           - Living standards poverty
           - Multidimensional poverty
        """)
    else:
        st.markdown("""
        This system uses machine learning to classify households based on:
        
        1. Income prediction using Linear Regression
        2. Income poverty classification using a threshold
        3. Detailed dimension analysis using Random Forest classifiers for:
           - Education poverty
           - Health poverty
           - Living standards poverty
           - Multidimensional poverty
        """)
    
    # Grouped Poverty Dimension Thresholds
    st.header("Poverty Dimension Thresholds")
    st.markdown("**Economic Thresholds**")
    st.session_state['living_standards_poverty_threshold'] = st.slider(
        "Living Standards Poverty Threshold", 0.0, 1.0, st.session_state['living_standards_poverty_threshold'], 0.01)
    st.markdown("**Health Thresholds**")
    st.session_state['health_poverty_threshold'] = st.slider(
        "Health Poverty Threshold", 0.0, 1.0, st.session_state['health_poverty_threshold'], 0.01)
    st.markdown("**Education Thresholds**")
    st.session_state['education_poverty_threshold'] = st.slider(
        "Education Poverty Threshold", 0.0, 1.0, st.session_state['education_poverty_threshold'], 0.01)
    st.markdown("**Multidimensional Thresholds**")
    st.session_state['multidimensional_poverty_threshold'] = st.slider(
        "Multidimensional Poverty Threshold", 0.0, 1.0, st.session_state['multidimensional_poverty_threshold'], 0.01)

    # Adjustable poverty threshold input
    if st.session_state.models_trained:
        st.header("Parameters")
        custom_threshold = st.number_input(
            "Custom Poverty Threshold (LKR)", 
            min_value=10000, 
            max_value=200000, 
            value=st.session_state.threshold, 
            step=5000,
            key="sidebar_income_poverty_threshold"
        )
        st.session_state.threshold = custom_threshold

    # Indicator-Specific Thresholds Guidance
    st.markdown("""
---
**Indicator-Specific Thresholds (Guidance):**

**Economic Indicators**
- **Number of Employed:** ≤ 1  
  _Households with only one or no employed members are at higher risk._
- **Business/Other Debt:** Any amount above 0 LKR  
  _Having any business or other debt increases economic vulnerability._
- **Time to Nearest Market:** > 25 minutes  
  _Longer travel times mean less access to essential goods._
- **Time to Nearest Bus Halt:** > 15 minutes  
  _Limited transport access can affect opportunities._

**Health Indicators**
- **Time to Nearest Hospital:** > 30 minutes  
  _Longer travel times can delay medical care._
- **Medical Debt:** > 30,000 LKR  
  _High medical debt signals health-related financial stress._
- **Chronic Illness:** Yes  
  _Presence of chronic illness increases health vulnerability._
- **Health Insurance:** No  
  _Lack of insurance increases risk._

**Education Indicators**
- **Highest Education Level:** Below O/L Passed  
  _Lower education levels limit opportunities._
- **Average Years of Education:** < 8 years  
  _Fewer years of education indicate disadvantage._
- **School Attendance Gap:** Not all school-age children attend  
  _Gaps in attendance can affect future prospects._

**Living Standards Indicators**
- **Housing Quality:** Below \"Average\"  
  _Poor housing quality affects well-being._
- **Adequate Sanitation:** No  
  _Lack of sanitation increases health risks._
- **Good Drinking Water:** No  
  _Unsafe water sources are a health hazard._
- **Durable Goods:** Missing refrigerator, computer/laptop, washing machine, or vehicle  
  _Lack of these items signals lower living standards._

*Use these thresholds as a guide to interpret the model’s results and adjust the sliders above to match your local context or policy needs.*
""")
    # Add developer credit in small text at the bottom of the sidebar
    st.markdown('<div style="font-size:11px; color:#ccc; margin-top:30px;">Developed by Kalana Geethmal<br>© All rights reserved. Unauthorized use or reproduction is prohibited.</div>', unsafe_allow_html=True)

# Create an interface for household information input
st.header("Household Information Input")
st.markdown("""
Enter household information below to predict income and classify poverty dimensions.
Fill in as many fields as possible for more accurate predictions.
**Input values are automatically used for predictions as you change them.**
""")

# Create tabs for different category inputs
demo_tab, edu_tab, health_tab, living_tab, economic_tab = st.tabs([
    "Demographic", "Education", "Health", "Living Standards", "Economic"
])

# Demographic Information tab
with demo_tab:
    st.subheader("Demographic Information")
    col1, col2 = st.columns(2)
    
    with col1:
        hhsize = st.number_input("Household Size (Number of People)", min_value=1, max_value=15, value=4)
        house_size_sqft = st.number_input("House Size (Square Feet)", min_value=100, max_value=10000, value=800, 
                                        help="Size of the dwelling in square feet")
        # Calculate square feet per person (for density measure)
        sqft_per_person = house_size_sqft / max(1, hhsize)
    
    with col2:
        district = st.selectbox(
            "District",
            options=[
                "Colombo", "Gampaha", "Kalutara", "Kandy", "Matale", "Nuwara Eliya",
                "Galle", "Matara", "Hambantota", "Jaffna", "Kilinochchi", "Mannar",
                "Vavuniya", "Mullaitivu", "Batticaloa", "Ampara", "Trincomalee",
                "Kurunegala", "Puttalam", "Anuradhapura", "Polonnaruwa", "Badulla",
                "Monaragala", "Ratnapura", "Kegalle"
            ],
            index=0
        )
        # Show district impact if models are trained
        if st.session_state.models_trained:
            # Try to get the training data
            if st.session_state.using_custom_data and 'X' in st.session_state and 'y_dict' in st.session_state:
                X = st.session_state.X.copy()
                y_income = st.session_state.y_dict.get('hhincomepm', None)
                y_poverty = st.session_state.y_dict.get('income_poor', None)
            elif not st.session_state.using_custom_data:
                # Use sample data
                from utils.utils import generate_sample_data
                sample_data = generate_sample_data(n_samples=2000)
                X = sample_data
                y_income = sample_data['hhincomepm']
                y_poverty = sample_data['income_poor']
            else:
                X, y_income, y_poverty = None, None, None
            if X is not None and y_income is not None:
                # Try to find the right column for district
                district_col = None
                for col in X.columns:
                    if 'district' in col:
                        district_col = col
                        break
                if district_col:
                    mask = X[district_col] == list(X[district_col].unique())[list(X[district_col].unique()).index(district)]
                    avg_income = y_income[mask].mean()
                    poverty_rate = y_poverty[mask].mean() * 100 if y_poverty is not None else None
                    st.info(f"**District Impact:** Average income: LKR {avg_income:,.0f} | Poverty rate: {poverty_rate:.1f}%")

        sector = st.selectbox(
            "Sector",
            options=["Urban", "Rural", "Estate"],
            index=1
        )
        # Show sector impact if models are trained
        if st.session_state.models_trained:
            if X is not None and y_income is not None:
                sector_col = None
                for col in X.columns:
                    if 'sector' in col:
                        sector_col = col
                        break
                if sector_col:
                    mask = X[sector_col] == list(X[sector_col].unique())[list(X[sector_col].unique()).index(sector)]
                    avg_income = y_income[mask].mean()
                    poverty_rate = y_poverty[mask].mean() * 100 if y_poverty is not None else None
                    st.info(f"**Sector Impact:** Average income: LKR {avg_income:,.0f} | Poverty rate: {poverty_rate:.1f}%")
        
        st.metric(
            "Square Feet per Person", 
            f"{sqft_per_person:.1f} sq.ft.",
            help="Living space per household member. Values below 100 sq.ft. per person indicate possible overcrowding."
        )

# Education tab
with edu_tab:
    st.subheader("Education Indicators")
    col1, col2 = st.columns(2)
    
    with col1:
        education_level_max = st.selectbox(
            "Highest Education Level in Household",
            options=["No formal education", "Primary (Grade 1-5)", "Lower Secondary (Grade 6-9)", 
                    "O/L Passed", "A/L Passed", "Degree/Diploma", "Postgraduate"],
            index=3
        )
        
        education_years_mean = st.slider(
            "Average Years of Education in Household", 
            min_value=0.0, 
            max_value=16.0, 
            value=8.0,
            step=0.5
        )
    
    with col2:
        school_age_children = st.number_input("Number of School-Age Children (5-18 years)", min_value=0, max_value=10, value=1)
        
        if school_age_children > 0:
            children_attending = st.number_input("Number of Children Attending School", min_value=0, max_value=int(school_age_children), value=int(school_age_children))
            school_attendance_ratio = children_attending / max(1, school_age_children)
        else:
            school_attendance_ratio = 1.0
        
        education_debt = st.number_input("Education-related Debt (LKR)", min_value=0, value=0, step=10000)

# Health tab
with health_tab:
    st.subheader("Health Indicators")
    col1, col2 = st.columns(2)
    
    with col1:
        chronic_illness = st.checkbox("Any Household Member has Chronic Illness", value=False)
        health_insurance = st.checkbox("Any Household Member has Health Insurance", value=False)
        disability = st.checkbox("Any Household Member has Disability", value=False)
    
    with col2:
        hospital_time = st.slider("Time to Nearest Hospital (minutes)", min_value=0, max_value=120, value=30)
        medical_debt = st.number_input("Medical-related Debt (LKR)", min_value=0, value=0, step=10000)

# Living Standards tab
with living_tab:
    st.subheader("Living Standards")
    col1, col2 = st.columns(2)
    
    with col1:
        housing_quality = st.selectbox(
            "Housing Quality",
            options=["Very Poor", "Poor", "Average", "Good", "Very Good"],
            index=2
        )
        
        housing_quality_index = {"Very Poor": 2, "Poor": 4, "Average": 6, "Good": 8, "Very Good": 10}[housing_quality]
        
        adequate_sanitation = st.checkbox("Has Adequate Sanitation Facilities", value=True)
        good_drinking_water = st.checkbox("Has Good Drinking Water Source", value=True)
        
        housing_debt = st.number_input("Housing-related Debt (LKR)", min_value=0, value=0, step=50000)
    
    with col2:
        # Durable goods
        st.write("Household Durable Goods:")
        has_refrigerator = st.checkbox("Refrigerator", value=False)
        has_tv = st.checkbox("Television", value=True)
        has_phone = st.checkbox("Mobile Phone", value=True)
        has_computer = st.checkbox("Computer/Laptop", value=False)
        has_washing_machine = st.checkbox("Washing Machine", value=False)
        has_vehicle = st.checkbox("Vehicle (Car/Motorcycle)", value=False)
        
        # Calculate weighted durable index
        durable_weights = {
            "refrigerator": 3, "tv": 2, "phone": 1, 
            "computer": 4, "washing_machine": 3, "vehicle": 5
        }
        
        weighted_durable_index = sum([
            durable_weights["refrigerator"] * int(has_refrigerator),
            durable_weights["tv"] * int(has_tv),
            durable_weights["phone"] * int(has_phone),
            durable_weights["computer"] * int(has_computer),
            durable_weights["washing_machine"] * int(has_washing_machine),
            durable_weights["vehicle"] * int(has_vehicle)
        ])

# Economic tab
with economic_tab:
    st.subheader("Economic Indicators")
    col1, col2 = st.columns(2)
    
    with col1:
        num_employed = st.number_input("Number of Employed Individuals", min_value=0, max_value=10, value=1)
        employment_type = st.selectbox(
            "Main Employment Type",
            options=["Government", "Semi-government", "Private", "Self-employed", "Unpaid family worker", "Unemployed"],
            index=2
        )
        
        business_debt = st.number_input("Business-related Debt (LKR)", min_value=0, value=0, step=50000)
        other_debt = st.number_input("Other Debt (LKR)", min_value=0, value=0, step=10000)
    
    with col2:
        # Accessibility times
        st.write("Accessibility (time in minutes):")
        bus_halt_time = st.slider("Time to Nearest Bus Halt", min_value=0, max_value=60, value=15)
        primary_school_time = st.slider("Time to Nearest Primary School", min_value=0, max_value=60, value=20)
        secondary_school_time = st.slider("Time to Nearest Secondary School", min_value=0, max_value=90, value=30)
        market_time = st.slider("Time to Nearest Market", min_value=0, max_value=60, value=25)

# Process input data immediately when any value changes
# Make sure models are trained before making predictions
if not st.session_state.models_trained:
    st.error("Please train the models first by uploading a dataset or using the default models.")
else:
    # Prepare input data
    input_data = {
        # Demographic features
        'hhsize': hhsize,
        'house_size_sqft': house_size_sqft,
        'sqft_per_person': sqft_per_person,
        'district_encoded': list(["Colombo", "Gampaha", "Kalutara", "Kandy", "Matale", "Nuwara Eliya",
                               "Galle", "Matara", "Hambantota", "Jaffna", "Kilinochchi", "Mannar",
                               "Vavuniya", "Mullaitivu", "Batticaloa", "Ampara", "Trincomalee",
                               "Kurunegala", "Puttalam", "Anuradhapura", "Polonnaruwa", "Badulla",
                               "Monaragala", "Ratnapura", "Kegalle"]).index(district),
        'sector_encoded': list(["Urban", "Rural", "Estate"]).index(sector),
        
        # Education features
        'education_level_max': list(["No formal education", "Primary (Grade 1-5)", "Lower Secondary (Grade 6-9)", 
                                  "O/L Passed", "A/L Passed", "Degree/Diploma", "Postgraduate"]).index(education_level_max),
        'education_years_mean': education_years_mean,
        'school_attendance_ratio': school_attendance_ratio,
        'education_debt': education_debt,
        
        # Health features
        'chronic_illness': int(chronic_illness),
        'health_insurance': int(health_insurance),
        'disability': int(disability),
        'hospital_time': hospital_time,
        'medical_debt': medical_debt,
        
        # Living standards features
        'housing_quality_index': housing_quality_index,
        'adequate_sanitation': int(adequate_sanitation),
        'good_drinking_water': int(good_drinking_water),
        'housing_debt': housing_debt,
        'weighted_durable_index': weighted_durable_index,
        'has_refrigerator': int(has_refrigerator),
        'has_tv': int(has_tv),
        'has_phone': int(has_phone),
        'has_computer': int(has_computer),
        'has_washing_machine': int(has_washing_machine),
        'has_vehicle': int(has_vehicle),
        
        # Economic features
        'num_employed': num_employed,
        'employment_type_encoded': list(["Government", "Semi-government", "Private", "Self-employed", "Unpaid family worker", "Unemployed"]).index(employment_type),
        'business_debt': business_debt,
        'other_debt': other_debt,
        
        # Accessibility features
        'bus_halt_time': bus_halt_time,
        'primary_school_time': primary_school_time,
        'secondary_school_time': secondary_school_time,
        'market_time': market_time
    }
    
    # Create DataFrame from input and engineer additional features
    input_df = pd.DataFrame([input_data])
    
    # Apply feature engineering to create consistent features
    engineered_df = engineer_hies_features(input_df)
    
    # Calculate additional required features
    total_debt = (engineered_df['housing_debt'] + 
                 engineered_df['education_debt'] + 
                 engineered_df['medical_debt'] + 
                 engineered_df['business_debt'] + 
                 engineered_df['other_debt'])
    
    # Add debt-to-income using a default income value initially
    default_income = 50000  # Using default value for initial calculation
    engineered_df['debt_to_income'] = total_debt / default_income
    
    # Prepare features ensuring all required columns are present
    prepared_df = prepare_features(engineered_df)
    
    # Ensure feature columns match the trained model
    if 'hhincomepm' in st.session_state.models:
        required_features = st.session_state.models['hhincomepm'].feature_names_in_
        for feature in required_features:
            if feature not in prepared_df.columns:
                prepared_df[feature] = 0  # Add missing columns with default value
        
        # Reorder columns to match training data
        prepared_df = prepared_df.reindex(columns=required_features, fill_value=0)
    
    # Make predictions
    try:
        # Predict income if model exists
        if 'hhincomepm' in st.session_state.models:
            predicted_income = st.session_state.models['hhincomepm'].predict(prepared_df)[0]
            
            # Update debt-to-income with predicted income and recompute predictions
            engineered_df['debt_to_income'] = total_debt / max(predicted_income, 1)
            prepared_df = prepare_features(engineered_df)
            prepared_df = prepared_df.reindex(columns=required_features, fill_value=0)
            
            # Final income prediction
            predicted_income = st.session_state.models['hhincomepm'].predict(prepared_df)[0]
        else:
            predicted_income = st.session_state.models['income'].predict(prepared_df)[0]
        
        # Get poverty dimension predictions
        dimension_results = {}
        for model_name, model in st.session_state.models.items():
            if model_name.endswith('_poor'):
                prediction_prob = model.predict_proba(prepared_df)[0][1]
                threshold_key = f"{model_name.replace('_poor', '')}_poverty_threshold"
                threshold = st.session_state.get(threshold_key, 0.5)
                dimension_results[model_name] = prediction_prob >= threshold

        # Display results
        st.header("Analysis Results")

        # Income Prediction Section
        st.subheader("Income Prediction")
        st.metric(
            "Predicted Monthly Household Income",
            f"LKR {predicted_income:,.2f}"
        )

        # Plot income prediction vs poverty threshold
        fig_income = plot_income_prediction(predicted_income, st.session_state.threshold)
        st.pyplot(fig_income)

        # Determine poverty status
        is_poor = predicted_income < st.session_state.threshold
        if is_poor:
            st.error(f"⚠️ This household is classified as INCOME POOR (below threshold of LKR {st.session_state.threshold:,})")
        else:
            st.success(f"✅ This household is NOT INCOME POOR (above threshold of LKR {st.session_state.threshold:,})")

        # Poverty Dimension Analysis Section
        st.subheader("Poverty Classification by Dimension:")
        for dimension, result in dimension_results.items():
            dimension_name = dimension.replace('_poor', '').replace('_', ' ').title()
            if result:
                st.error(f"❌ {dimension_name} Poverty: POOR")
            else:
                st.success(f"✅ {dimension_name} Poverty: NOT POOR")

        # Get and display recommended interventions
        st.subheader("Recommended Interventions")
        interventions = get_recommended_interventions(
            is_income_poor=is_poor,
            dimension_results=dimension_results,
            household_data=input_data
        )
        for category, recommendations in interventions.items():
            with st.expander(f"{category} Interventions"):
                for rec in recommendations:
                    st.markdown(f"• {rec}")
    
    except Exception as e:
        st.error("Error generating predictions. Please check your input data.")
        st.exception(e)

st.markdown("""
---
**Developed by Kalana Geethmal**  
© All rights reserved. Unauthorized use or reproduction is prohibited.
""")