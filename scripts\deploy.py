#!/usr/bin/env python3
"""
Deployment script for Poverty Classification System
Handles database initialization, model setup, and service deployment
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
import argparse

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import settings
from src.core.logging import setup_logging

logger = setup_logging()


def run_command(command, check=True, shell=False):
    """Run shell command with logging"""
    logger.info(f"Running: {command}")
    try:
        if shell:
            result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        else:
            result = subprocess.run(command.split(), check=check, capture_output=True, text=True)
        
        if result.stdout:
            logger.info(f"Output: {result.stdout}")
        if result.stderr:
            logger.warning(f"Error output: {result.stderr}")
        
        return result
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {e}")
        if e.stdout:
            logger.error(f"Stdout: {e.stdout}")
        if e.stderr:
            logger.error(f"Stderr: {e.stderr}")
        raise


def check_dependencies():
    """Check if required dependencies are available"""
    logger.info("Checking dependencies...")
    
    # Check Python version
    if sys.version_info < (3, 9):
        raise RuntimeError("Python 3.9 or higher is required")
    
    # Check Docker
    try:
        run_command("docker --version")
        logger.info("✅ Docker is available")
    except subprocess.CalledProcessError:
        logger.warning("⚠️ Docker not available - manual deployment required")
    
    # Check Docker Compose
    try:
        run_command("docker-compose --version")
        logger.info("✅ Docker Compose is available")
    except subprocess.CalledProcessError:
        logger.warning("⚠️ Docker Compose not available - manual deployment required")


def setup_environment():
    """Setup environment variables and configuration"""
    logger.info("Setting up environment...")
    
    env_file = project_root / ".env"
    env_example = project_root / ".env.example"
    
    if not env_file.exists() and env_example.exists():
        logger.info("Creating .env file from .env.example")
        import shutil
        shutil.copy(env_example, env_file)
        logger.warning("⚠️ Please update .env file with your configuration before proceeding")
    
    # Create necessary directories
    directories = ["logs", "uploads", "models", "exports"]
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(exist_ok=True)
        logger.info(f"Created directory: {directory}")


def install_dependencies():
    """Install Python dependencies"""
    logger.info("Installing Python dependencies...")
    
    requirements_file = project_root / "requirements.txt"
    if requirements_file.exists():
        run_command(f"pip install -r {requirements_file}")
        logger.info("✅ Python dependencies installed")
    else:
        logger.error("❌ requirements.txt not found")
        raise FileNotFoundError("requirements.txt not found")


def initialize_database():
    """Initialize database and create tables"""
    logger.info("Initializing database...")
    
    init_script = project_root / "scripts" / "init_db.py"
    if init_script.exists():
        run_command(f"python {init_script}")
        logger.info("✅ Database initialized")
    else:
        logger.error("❌ Database initialization script not found")
        raise FileNotFoundError("init_db.py not found")


def build_docker_images():
    """Build Docker images"""
    logger.info("Building Docker images...")
    
    dockerfile = project_root / "Dockerfile"
    if dockerfile.exists():
        run_command("docker build -t poverty-classifier .")
        logger.info("✅ Docker image built")
    else:
        logger.error("❌ Dockerfile not found")
        raise FileNotFoundError("Dockerfile not found")


def deploy_with_docker():
    """Deploy using Docker Compose"""
    logger.info("Deploying with Docker Compose...")
    
    compose_file = project_root / "docker-compose.yml"
    if compose_file.exists():
        # Stop existing containers
        run_command("docker-compose down", check=False)
        
        # Start services
        run_command("docker-compose up -d")
        logger.info("✅ Services deployed with Docker Compose")
        
        # Wait for services to be ready
        import time
        logger.info("Waiting for services to start...")
        time.sleep(10)
        
        # Check service health
        check_service_health()
    else:
        logger.error("❌ docker-compose.yml not found")
        raise FileNotFoundError("docker-compose.yml not found")


def deploy_manual():
    """Manual deployment without Docker"""
    logger.info("Starting manual deployment...")
    
    # Install dependencies
    install_dependencies()
    
    # Initialize database
    initialize_database()
    
    logger.info("✅ Manual deployment completed")
    logger.info("To start the services manually:")
    logger.info("1. Start API: uvicorn src.api.main:app --host 0.0.0.0 --port 8000")
    logger.info("2. Start Frontend: streamlit run frontend/app.py --server.port 8501")


def check_service_health():
    """Check if deployed services are healthy"""
    logger.info("Checking service health...")
    
    import requests
    import time
    
    services = [
        {"name": "API", "url": f"http://localhost:8000/health"},
        {"name": "Frontend", "url": f"http://localhost:8501"}
    ]
    
    for service in services:
        max_retries = 5
        for attempt in range(max_retries):
            try:
                response = requests.get(service["url"], timeout=10)
                if response.status_code == 200:
                    logger.info(f"✅ {service['name']} is healthy")
                    break
                else:
                    logger.warning(f"⚠️ {service['name']} returned status {response.status_code}")
            except requests.exceptions.RequestException as e:
                logger.warning(f"⚠️ {service['name']} health check failed: {e}")
                if attempt < max_retries - 1:
                    logger.info(f"Retrying in 5 seconds... ({attempt + 1}/{max_retries})")
                    time.sleep(5)
                else:
                    logger.error(f"❌ {service['name']} health check failed after {max_retries} attempts")


def main():
    """Main deployment function"""
    parser = argparse.ArgumentParser(description="Deploy Poverty Classification System")
    parser.add_argument("--mode", choices=["docker", "manual"], default="docker",
                       help="Deployment mode (default: docker)")
    parser.add_argument("--skip-deps", action="store_true",
                       help="Skip dependency installation")
    parser.add_argument("--skip-db", action="store_true",
                       help="Skip database initialization")
    
    args = parser.parse_args()
    
    try:
        logger.info("🚀 Starting Poverty Classification System deployment...")
        
        # Check dependencies
        if not args.skip_deps:
            check_dependencies()
        
        # Setup environment
        setup_environment()
        
        if args.mode == "docker":
            # Docker deployment
            build_docker_images()
            deploy_with_docker()
        else:
            # Manual deployment
            deploy_manual()
        
        logger.info("🎉 Deployment completed successfully!")
        logger.info("Access the application at:")
        logger.info("- Web Interface: http://localhost:8501")
        logger.info("- API Documentation: http://localhost:8000/docs")
        
    except Exception as e:
        logger.error(f"❌ Deployment failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
