"""
Administration-related schemas
"""

from pydantic import BaseModel, EmailStr, validator
from typing import Optional, Dict, Any
from datetime import datetime


class SystemStats(BaseModel):
    """Schema for system statistics"""
    total_users: int
    active_users: int
    total_households: int
    households_last_30_days: int
    total_predictions: int
    predictions_last_30_days: int
    income_poor_percentage: float
    multidimensional_poor_percentage: float


class UserManagement(BaseModel):
    """Schema for user management"""
    id: int
    username: str
    email: str
    full_name: Optional[str] = None
    role: str
    is_active: bool
    is_verified: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    
    class Config:
        orm_mode = True


class UserCreate(BaseModel):
    """Schema for creating users (admin)"""
    username: str
    email: EmailStr
    password: str
    full_name: Optional[str] = None
    role: str = "viewer"
    
    @validator('username')
    def validate_username(cls, v):
        if len(v) < 3:
            raise ValueError('Username must be at least 3 characters long')
        if len(v) > 50:
            raise ValueError('Username must be less than 50 characters')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        return v
    
    @validator('role')
    def validate_role(cls, v):
        if v not in ['admin', 'analyst', 'viewer']:
            raise ValueError('Role must be admin, analyst, or viewer')
        return v


class UserUpdate(BaseModel):
    """Schema for updating users (admin)"""
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    role: Optional[str] = None
    is_active: Optional[bool] = None
    is_verified: Optional[bool] = None
    
    @validator('role')
    def validate_role(cls, v):
        if v is not None and v not in ['admin', 'analyst', 'viewer']:
            raise ValueError('Role must be admin, analyst, or viewer')
        return v


class AuditLogResponse(BaseModel):
    """Schema for audit log response"""
    id: int
    user_id: Optional[int] = None
    action: str
    resource_type: Optional[str] = None
    resource_id: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    status_code: Optional[int] = None
    timestamp: datetime
    duration_ms: Optional[int] = None
    
    class Config:
        orm_mode = True


class SystemMetricsResponse(BaseModel):
    """Schema for system metrics response"""
    id: int
    metric_name: str
    metric_value: float
    metric_unit: Optional[str] = None
    tags: Optional[Dict[str, Any]] = None
    timestamp: datetime
    
    class Config:
        orm_mode = True
