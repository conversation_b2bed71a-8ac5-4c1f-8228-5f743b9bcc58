"""
Prediction service for poverty classification
"""

import pandas as pd
import numpy as np
import pickle
import os
from typing import Dict, List, Any, Optional
from pathlib import Path
import logging

from config.settings import settings
from src.core.logging import get_logger

# Import the original models and utilities
import sys
sys.path.append(str(Path(__file__).parent.parent.parent))
from models import (
    create_income_model, 
    create_poverty_dimension_model,
    get_recommended_interventions
)
from utils.utils import prepare_features, generate_sample_data

logger = get_logger(__name__)


class PredictionService:
    """Service for handling poverty predictions"""
    
    def __init__(self):
        self.models = {}
        self.model_version = "1.0.0"
        self.poverty_threshold = settings.DEFAULT_POVERTY_THRESHOLD
        self._load_or_train_models()
    
    def _load_or_train_models(self):
        """Load existing models or train new ones"""
        try:
            model_dir = Path(settings.MODEL_DIR)
            model_dir.mkdir(exist_ok=True)
            
            # Try to load existing models
            model_files = {
                'income': model_dir / 'income_model.pkl',
                'education_poor': model_dir / 'education_poor_model.pkl',
                'health_poor': model_dir / 'health_poor_model.pkl',
                'living_standards_poor': model_dir / 'living_standards_poor_model.pkl',
                'multidimensionally_poor': model_dir / 'multidimensionally_poor_model.pkl'
            }
            
            models_loaded = True
            for model_name, model_file in model_files.items():
                if model_file.exists():
                    try:
                        with open(model_file, 'rb') as f:
                            self.models[model_name] = pickle.load(f)
                        logger.info(f"Loaded {model_name} model from {model_file}")
                    except Exception as e:
                        logger.error(f"Failed to load {model_name} model: {e}")
                        models_loaded = False
                        break
                else:
                    models_loaded = False
                    break
            
            if not models_loaded:
                logger.info("Training new models...")
                self._train_models()
                
        except Exception as e:
            logger.error(f"Error in model loading/training: {e}")
            # Train models as fallback
            self._train_models()
    
    def _train_models(self):
        """Train new models using sample data"""
        try:
            # Generate sample data for training
            sample_data = generate_sample_data(n_samples=2000)
            X = prepare_features(sample_data)
            
            # Define target columns
            target_columns = [
                'hhincomepm', 'income_poor', 'education_poor', 
                'health_poor', 'living_standards_poor', 'multidimensionally_poor'
            ]
            
            # Extract targets
            y_dict = {}
            for target in target_columns:
                if target in sample_data.columns:
                    y_dict[target] = sample_data[target]
            
            # Train models
            from sklearn.model_selection import train_test_split
            
            for target_name, target_values in y_dict.items():
                X_train, X_test, y_train, y_test = train_test_split(
                    X, target_values, test_size=0.2, random_state=42
                )
                
                if target_name == 'hhincomepm':
                    model = create_income_model(X_train, y_train)
                    self.models['income'] = model
                elif target_name.endswith('_poor'):
                    model = create_poverty_dimension_model(X_train, y_train)
                    self.models[target_name] = model
            
            # Save models
            self._save_models()
            logger.info("Models trained and saved successfully")
            
        except Exception as e:
            logger.error(f"Failed to train models: {e}")
            raise
    
    def _save_models(self):
        """Save trained models to disk"""
        try:
            model_dir = Path(settings.MODEL_DIR)
            model_dir.mkdir(exist_ok=True)
            
            model_files = {
                'income': model_dir / 'income_model.pkl',
                'education_poor': model_dir / 'education_poor_model.pkl',
                'health_poor': model_dir / 'health_poor_model.pkl',
                'living_standards_poor': model_dir / 'living_standards_poor_model.pkl',
                'multidimensionally_poor': model_dir / 'multidimensionally_poor_model.pkl'
            }
            
            for model_name, model_file in model_files.items():
                if model_name in self.models:
                    with open(model_file, 'wb') as f:
                        pickle.dump(self.models[model_name], f)
                    logger.info(f"Saved {model_name} model to {model_file}")
                    
        except Exception as e:
            logger.error(f"Failed to save models: {e}")
    
    def _prepare_input_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare input data for prediction"""
        try:
            # Ensure required columns exist with default values
            required_columns = {
                'hhsize': 'household_size',
                'district_encoded': 'district_encoded',
                'education_level_max': 'education_level_max',
                'education_years_mean': 'education_years_mean',
                'school_attendance_ratio': 'school_attendance_ratio',
                'chronic_illness': 'chronic_illness',
                'health_insurance': 'health_insurance',
                'disability': 'disability',
                'hospital_time': 'hospital_time',
                'housing_quality_index': 'housing_quality_index',
                'adequate_sanitation': 'adequate_sanitation',
                'good_drinking_water': 'good_drinking_water',
                'weighted_durable_index': 'weighted_durable_index',
                'num_employed': 'num_employed',
                'bus_halt_time': 'bus_halt_time',
                'market_time': 'market_time',
                'school_time': 'school_time',
                'education_debt': 'education_debt',
                'medical_debt': 'medical_debt',
                'housing_debt': 'housing_debt',
                'business_debt': 'business_debt',
                'other_debt': 'other_debt'
            }
            
            # Map column names and fill missing values
            for model_col, input_col in required_columns.items():
                if input_col in df.columns:
                    df[model_col] = df[input_col]
                elif model_col not in df.columns:
                    # Set default values based on column type
                    if model_col in ['chronic_illness', 'health_insurance', 'disability', 
                                   'adequate_sanitation', 'good_drinking_water']:
                        df[model_col] = False
                    elif model_col in ['education_debt', 'medical_debt', 'housing_debt', 
                                     'business_debt', 'other_debt']:
                        df[model_col] = 0.0
                    elif model_col == 'district_encoded':
                        df[model_col] = 1  # Default district
                    elif model_col == 'num_employed':
                        df[model_col] = 1  # Default employed
                    else:
                        df[model_col] = df[model_col].fillna(df[model_col].median() if df[model_col].dtype in ['float64', 'int64'] else 0)
            
            # Apply feature preparation
            prepared_df = prepare_features(df)
            
            return prepared_df
            
        except Exception as e:
            logger.error(f"Failed to prepare input data: {e}")
            raise
    
    async def predict_single(self, input_df: pd.DataFrame) -> Dict[str, Any]:
        """Make prediction for a single household"""
        try:
            # Prepare data
            prepared_df = self._prepare_input_data(input_df)
            
            # Make income prediction
            if 'income' in self.models:
                predicted_income = float(self.models['income'].predict(prepared_df)[0])
            else:
                predicted_income = 50000.0  # Default fallback
            
            # Determine income poverty
            income_poor = predicted_income < self.poverty_threshold
            
            # Make poverty dimension predictions
            dimension_results = {}
            probabilities = {}
            
            for dimension in ['education_poor', 'health_poor', 'living_standards_poor', 'multidimensionally_poor']:
                if dimension in self.models:
                    prediction = self.models[dimension].predict(prepared_df)[0]
                    probability = self.models[dimension].predict_proba(prepared_df)[0][1]
                    
                    dimension_results[dimension] = bool(prediction)
                    probabilities[f"{dimension}_probability"] = float(probability)
                else:
                    dimension_results[dimension] = False
                    probabilities[f"{dimension}_probability"] = 0.0
            
            # Get recommendations
            recommendations = get_recommended_interventions(dimension_results)
            
            # Calculate confidence (average of probabilities)
            confidence = np.mean(list(probabilities.values()))
            
            result = {
                'predicted_income': predicted_income,
                'income_poor': income_poor,
                'poverty_threshold': self.poverty_threshold,
                'model_version': self.model_version,
                'confidence': confidence,
                'recommendations': recommendations,
                **dimension_results,
                **probabilities
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            raise
    
    async def predict_batch(self, input_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Make predictions for multiple households"""
        try:
            results = []
            
            for idx in range(len(input_df)):
                household_df = input_df.iloc[[idx]]
                prediction = await self.predict_single(household_df)
                results.append(prediction)
            
            return results
            
        except Exception as e:
            logger.error(f"Batch prediction failed: {e}")
            raise
    
    def retrain_models(self, training_data: pd.DataFrame):
        """Retrain models with new data"""
        try:
            logger.info("Starting model retraining...")
            
            # Prepare features
            X = prepare_features(training_data)
            
            # Extract targets
            target_columns = [
                'hhincomepm', 'income_poor', 'education_poor', 
                'health_poor', 'living_standards_poor', 'multidimensionally_poor'
            ]
            
            y_dict = {}
            for target in target_columns:
                if target in training_data.columns:
                    y_dict[target] = training_data[target]
            
            # Retrain models
            from sklearn.model_selection import train_test_split
            
            for target_name, target_values in y_dict.items():
                X_train, X_test, y_train, y_test = train_test_split(
                    X, target_values, test_size=0.2, random_state=42
                )
                
                if target_name == 'hhincomepm':
                    model = create_income_model(X_train, y_train)
                    self.models['income'] = model
                elif target_name.endswith('_poor'):
                    model = create_poverty_dimension_model(X_train, y_train)
                    self.models[target_name] = model
            
            # Save retrained models
            self._save_models()
            logger.info("Models retrained successfully")
            
        except Exception as e:
            logger.error(f"Model retraining failed: {e}")
            raise
