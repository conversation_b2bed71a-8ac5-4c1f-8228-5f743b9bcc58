"""
Prediction-related schemas
"""

from pydantic import BaseModel, validator
from typing import Optional, Dict, Any, List
from datetime import datetime


class HouseholdInput(BaseModel):
    """Schema for household input data"""
    household_size: int
    district: Optional[str] = None
    
    # Education
    education_level_max: Optional[int] = None
    education_years_mean: Optional[float] = None
    school_attendance_ratio: Optional[float] = None
    education_debt: Optional[float] = 0.0
    
    # Health
    chronic_illness: Optional[bool] = False
    health_insurance: Optional[bool] = False
    disability: Optional[bool] = False
    hospital_time: Optional[float] = None
    medical_debt: Optional[float] = 0.0
    
    # Living standards
    housing_quality_index: Optional[float] = None
    adequate_sanitation: Optional[bool] = False
    good_drinking_water: Optional[bool] = False
    weighted_durable_index: Optional[float] = None
    housing_debt: Optional[float] = 0.0
    
    # Economic
    num_employed: Optional[int] = 0
    business_debt: Optional[float] = 0.0
    other_debt: Optional[float] = 0.0
    
    # Accessibility
    bus_halt_time: Optional[float] = None
    market_time: Optional[float] = None
    school_time: Optional[float] = None
    
    @validator('household_size')
    def validate_household_size(cls, v):
        if v < 1 or v > 20:
            raise ValueError('Household size must be between 1 and 20')
        return v
    
    @validator('education_level_max')
    def validate_education_level(cls, v):
        if v is not None and (v < 0 or v > 20):
            raise ValueError('Education level must be between 0 and 20')
        return v
    
    @validator('education_years_mean')
    def validate_education_years(cls, v):
        if v is not None and (v < 0 or v > 25):
            raise ValueError('Education years must be between 0 and 25')
        return v
    
    @validator('school_attendance_ratio')
    def validate_attendance_ratio(cls, v):
        if v is not None and (v < 0 or v > 1):
            raise ValueError('School attendance ratio must be between 0 and 1')
        return v
    
    @validator('housing_quality_index')
    def validate_housing_quality(cls, v):
        if v is not None and (v < 0 or v > 10):
            raise ValueError('Housing quality index must be between 0 and 10')
        return v
    
    @validator('weighted_durable_index')
    def validate_durable_index(cls, v):
        if v is not None and (v < 0 or v > 10):
            raise ValueError('Durable index must be between 0 and 10')
        return v


class PredictionResponse(BaseModel):
    """Schema for prediction response"""
    prediction_id: str
    household_id: str
    
    # Predictions
    predicted_income: float
    income_poor: bool
    education_poor: bool
    health_poor: bool
    living_standards_poor: bool
    multidimensionally_poor: bool
    
    # Probabilities
    probabilities: Dict[str, Optional[float]]
    
    # Metadata
    confidence: Optional[float] = None
    recommendations: Optional[Dict[str, Any]] = None
    model_version: Optional[str] = None
    poverty_threshold: Optional[float] = None
    created_at: datetime
    
    class Config:
        orm_mode = True


class BatchPredictionRequest(BaseModel):
    """Schema for batch prediction request"""
    households: List[HouseholdInput]
    
    @validator('households')
    def validate_batch_size(cls, v):
        if len(v) > 1000:
            raise ValueError('Batch size cannot exceed 1000 households')
        if len(v) == 0:
            raise ValueError('At least one household is required')
        return v


class BatchPredictionResponse(BaseModel):
    """Schema for batch prediction response"""
    batch_id: str
    total_predictions: int
    successful_predictions: int
    failed_predictions: int
    predictions: List[PredictionResponse]


class PredictionHistory(BaseModel):
    """Schema for prediction history"""
    prediction_id: str
    household_id: str
    predicted_income: float
    income_poor: bool
    multidimensionally_poor: bool
    created_at: datetime
    
    class Config:
        orm_mode = True
